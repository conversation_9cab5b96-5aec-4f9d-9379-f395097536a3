package com.btpnsyariah.agendaku.cashmanager.personas;

import com.btpnsyariah.agendaku.cashmanager.notification.service.NotificationConfiguratorService;
import com.btpnsyariah.agendaku.cashmanager.notification.service.NotificationService;
import com.btpnsyariah.agendaku.cashmanager.personas.ActivationReport.ActivationReportEntity;
import com.btpnsyariah.agendaku.cashmanager.sprint28.transaction.common.authorization.UserAuthorizationServiceV1;
import com.btpnsyariah.agendaku.cashmanager.uservalidator.UserDetailEntity;
import com.btpnsyariah.agendaku.cashmanager.util.DataIntegrityException;
import com.btpnsyariah.agendaku.cashmanager.util.DataNotFoundException;
import com.btpnsyariah.agendaku.cashmanager.util.DatabaseTransactionException;

import javassist.NotFoundException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@RestController
public class CashUserPersonasController {

    private static final Logger CASH_HOME_LOGGER = LoggerFactory.getLogger(CashUserPersonasController.class);
    private static final String RESET = "RESET";

    @Autowired
    private CashUserPersonasService cashUserPersonasService;

    @Autowired
    private UserAuthorizationServiceV1 userAuthorizationService;

    @Autowired
    private NotificationConfiguratorService notificationConfiguratorService;

    @Autowired
    private NotificationService notificationService;

    @GetMapping("/cos/{mmsCode}")
    @CrossOrigin(origins = {"https://cash-management-agendaku-dirty.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://cash-management-agendaku-beta.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://core-agendaku-dirty.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://core-agendaku-beta.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://core-agendaku-south.apps.south.syariahbtpn.com",
            "https://core-agendaku-north.apps.north.syariahbtpn.com",
            "http://localhost:3000", "http://localhost:3001",
            "https://agendaku.apps.btpnsyariah.com"})
    public ResponseEntity<List<CashUserPersonasEntity>> getMappedCosForGivenMms(@PathVariable String mmsCode) {
        List<CashUserPersonasEntity> coverDanaMappings = cashUserPersonasService.fetchAllUserPersonasForMms(mmsCode);
        CASH_HOME_LOGGER.info("Fetched the list of COs mapped to the MMSCode : {}. SIZE= {}", mmsCode, coverDanaMappings.size());
        return ResponseEntity.status(HttpStatus.OK).body(coverDanaMappings);
    }

    @GetMapping("/personas/list")
    @CrossOrigin(origins = {"https://cash-management-agendaku-dirty.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://cash-management-agendaku-beta.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://core-agendaku-dirty.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://core-agendaku-beta.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://core-agendaku-south.apps.south.syariahbtpn.com",
            "https://core-agendaku-north.apps.north.syariahbtpn.com",
            "http://localhost:3000", "http://localhost:3001",
            "https://agendaku.apps.btpnsyariah.com"})
    public ResponseEntity<List<CashUserPersonasEntity>> getMappedUsersForGivenMms(@RequestParam String mmsCode) {
        try {
            return ResponseEntity.status(HttpStatus.OK).body(cashUserPersonasService.fetchAllUserPersonasByMms(mmsCode));
        } catch (Exception e) {
            CASH_HOME_LOGGER.info("Error while fetching the list of users mapped to the MMSCode : {}, Error : {}", mmsCode, e);
            return new ResponseEntity(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR) ;
        }
    }

    @PatchMapping("personas/piloting-toggle")
    @CrossOrigin(origins = {"https://cash-management-agendaku-dirty.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://cash-management-agendaku-beta.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://core-agendaku-dirty.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://core-agendaku-beta.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://core-agendaku-south.apps.south.syariahbtpn.com",
            "https://core-agendaku-north.apps.north.syariahbtpn.com",
            "http://localhost:3000", "http://localhost:3001",
            "https://agendaku.apps.btpnsyariah.com"})
    public ResponseEntity setPilotingToggle(@RequestBody HashMap<String, Boolean> personas) {
        try {
            cashUserPersonasService.setPilotingToggle(personas);
            return ResponseEntity.status(HttpStatus.OK).body("Toggle set successfully!");
        } catch (Exception e) {
            CASH_HOME_LOGGER.info("Error while setting users toggles : " + e);
            return new ResponseEntity(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR) ;
        }
    }

    @GetMapping("/kas/cos")
    public ResponseEntity<Map<String, List<CashUserPersonasEntity>>> getMappedCosForMmsBelongingToNik(@SessionAttribute("userDetail") UserDetailEntity userDetail) throws NotFoundException {
        Map<String, List<CashUserPersonasEntity>> coverDanaMappings = cashUserPersonasService.fetchAllUserPersonasForNik(userDetail.getUserIdentifier(), userDetail.getCoCode());
        CASH_HOME_LOGGER.info("Fetched the list of COs mapped to the respective MMSCode for given Nik: {}. MAP_SIZE= {}", userDetail.getUserIdentifier(), coverDanaMappings.size());
        return ResponseEntity.status(HttpStatus.OK).body(coverDanaMappings);
    }

    @GetMapping("/personas/filter")
    @CrossOrigin(origins = {"https://cash-management-agendaku-dirty.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://cash-management-agendaku-beta.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://core-agendaku-dirty.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://core-agendaku-beta.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://core-agendaku-south.apps.south.syariahbtpn.com",
            "https://core-agendaku-north.apps.north.syariahbtpn.com",
            "http://localhost:3000", "http://localhost:3001",
            "https://agendaku.apps.btpnsyariah.com"})
    public ResponseEntity<Page<CashUserPersonasEntity>> getCashUserPersonas(@RequestParam final String queryString, Pageable pageable, @RequestParam String access, @RequestParam String mmsCode) {
        Page<CashUserPersonasEntity> cashUserPersonas=null;
        if(access.equalsIgnoreCase("ADMIN")){
           cashUserPersonas = cashUserPersonasService.fetchAllUserPersonas(queryString, pageable);
        } else if(access.equalsIgnoreCase("BMADMIN")) {
            cashUserPersonas = cashUserPersonasService.fetchAllUserPersonasBm(queryString, pageable, mmsCode);
        }
        CASH_HOME_LOGGER.info("Fetched the list of personas for given queryString: {}", queryString);
        return ResponseEntity.status(HttpStatus.OK).body(cashUserPersonas);
    }

    @GetMapping("/kas/logs/{mmsCode}")
    @CrossOrigin(origins = {"https://cash-management-agendaku-dirty.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://cash-management-agendaku-beta.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://core-agendaku-dirty.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://core-agendaku-beta.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://core-agendaku-south.apps.south.syariahbtpn.com",
            "https://core-agendaku-north.apps.north.syariahbtpn.com",
            "http://localhost:3000", "http://localhost:3001",
            "https://agendaku.apps.btpnsyariah.com"})
    public ResponseEntity<List<CashUserLog>> getLogDetailsForUserChange(@PathVariable String mmsCode) {
        CASH_HOME_LOGGER.info("Fetched the list of Logs for User Change for MMSCode: {}", mmsCode);
        List<CashUserLog> list = cashUserPersonasService.fetchAllLogsOfUserChanges(mmsCode);
        if(list.size() == 0) {
            ResponseEntity.status(HttpStatus.NOT_FOUND).body(list);
        }
        return ResponseEntity.status(HttpStatus.OK).body(list);
    }

    @GetMapping("/kas/logs/date/{mmsCode}")
    @CrossOrigin(origins = {"https://cash-management-agendaku-dirty.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://cash-management-agendaku-beta.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://core-agendaku-dirty.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://core-agendaku-beta.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://core-agendaku-south.apps.south.syariahbtpn.com",
            "https://core-agendaku-north.apps.north.syariahbtpn.com",
            "http://localhost:3000", "http://localhost:3001",
            "https://agendaku.apps.btpnsyariah.com"})
    public ResponseEntity<List<CashUserLog>> getLogDetailsForUserChangeByDate(@PathVariable String mmsCode,@RequestParam(value = "dateFrom") String dateFrom, @RequestParam(value = "dateTo") String dateTo) {
        CASH_HOME_LOGGER.info("Fetched the list of Logs for User Change for MMSCode: {}", mmsCode);
        List<CashUserLog> list = cashUserPersonasService.fetchAllLogsOfUserChangesByDateRange(mmsCode,dateFrom,dateTo);
        if(list.size() == 0) {
            ResponseEntity.status(HttpStatus.NOT_FOUND).body(list);
        }
        return ResponseEntity.status(HttpStatus.OK).body(list);
    }

    @GetMapping("/personas/role/{mmsCode}/{role}")
    public ResponseEntity<List<CashUserPersonasDto>> getLogDetailsForUserChange(@PathVariable String mmsCode, @PathVariable String role) {
        List<CashUserPersonasDto> list = cashUserPersonasService.fetchAllPersonaBymmsCodeAndRole(mmsCode,role);
        if(list.size() == 0) {
            CASH_HOME_LOGGER.info("User not found for MMSCode: {} and Role : {}", mmsCode,role);
            ResponseEntity.status(HttpStatus.NOT_FOUND).body(list);
        }
        CASH_HOME_LOGGER.info("Fetched the list of User for MMSCode: {} and Role : {}", mmsCode,role);
        return ResponseEntity.status(HttpStatus.OK).body(list);
    }

    @GetMapping("/personas/multiplerole/{mmsCode}")
    public ResponseEntity<List<CashUserPersonasEntity>> getLogDetailsForUserChange(@PathVariable String mmsCode, @RequestBody List<String> roles) {
        List<CashUserPersonasEntity> list = cashUserPersonasService.fetchAllPersonaBymmsCodeAndRoles(mmsCode,roles);
        if(list.size() == 0) {
            CASH_HOME_LOGGER.info("User not found for MMSCode: {} for multiple role", mmsCode);
            ResponseEntity.status(HttpStatus.NOT_FOUND).body(list);
        }
        CASH_HOME_LOGGER.info("Fetched the list of User for MMSCode: {} for multiple role", mmsCode);
        return ResponseEntity.status(HttpStatus.OK).body(list);
    }

    @PostMapping("/persona/assign/hubco/{mmsCode}")
    public ResponseEntity<String> persistCoverDanaMapping(@PathVariable String mmsCode, @RequestBody List<CashUserPersonasEntity> cashusers) {
        try {
            cashUserPersonasService.updatePersonaMMSChild(mmsCode,cashusers);
        }catch (DataIntegrityException e){
            CASH_HOME_LOGGER.error("DataIntegrityException with message {}", e.getMessage());
            return new ResponseEntity("DataIntegrityException causing data cannot be saved", HttpStatus.NOT_ACCEPTABLE);
        }catch (DataNotFoundException e){
            CASH_HOME_LOGGER.error("DataNotFoundException with message {}", e.getMessage());
            return new ResponseEntity("DataNotFoundException causing data cannot be saved", HttpStatus.BAD_REQUEST);
        }catch (Exception e){
            CASH_HOME_LOGGER.error("Unhandled Exception with message {}", e.getMessage());
            return new ResponseEntity("Unhandled Exception causing data cannot be saved", HttpStatus.INTERNAL_SERVER_ERROR);
        }
        String message = "Assign hub co successfully persisted";
        CASH_HOME_LOGGER.info(message);
        return new ResponseEntity<>(message, HttpStatus.OK);
    }

    @GetMapping("/persona/activation/report")
    @CrossOrigin(origins = {"https://cash-management-agendaku-dirty.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://cash-management-agendaku-beta.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://core-agendaku-dirty.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://core-agendaku-beta.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://core-agendaku-south.apps.south.syariahbtpn.com",
            "https://core-agendaku-north.apps.north.syariahbtpn.com",
            "http://localhost:3000", "http://localhost:3001",
            "https://agendaku.apps.btpnsyariah.com"})
    public ResponseEntity<List<ActivationReportEntity>> getReportActivation(@RequestParam(value = "dateFrom") String dateFrom, @RequestParam(value = "dateTo") String dateTo, @RequestParam("flag") String flag, @RequestParam("value") String value){
        //flag = ALL (search all), flag = mms (search by mms code), flag = kfo (search by kfo code), flag = cocode (search by co code)
        List<ActivationReportEntity> list = cashUserPersonasService.getActivationReportByFlag(flag, value, dateFrom, dateTo);
        CASH_HOME_LOGGER.info("getting list wow activation report");
        return new ResponseEntity<>(list,HttpStatus.OK);
    }

    @GetMapping("/persona/list/user")
    @CrossOrigin(origins = {"https://cash-management-agendaku-dirty.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://cash-management-agendaku-beta.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://core-agendaku-dirty.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://core-agendaku-beta.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://core-agendaku-south.apps.south.syariahbtpn.com",
            "https://core-agendaku-north.apps.north.syariahbtpn.com",
            "http://localhost:3000", "http://localhost:3001",
            "https://agendaku.apps.btpnsyariah.com"})
    public ResponseEntity<List<CashUserPersonasEntity>> getCashUser(@RequestParam String value, @RequestParam String flag){
        // 1 search by nik, 2 search by co code, 3 search by mms
        List<CashUserPersonasEntity> listUser = cashUserPersonasService.getListCashUserPersonasByFlag(value,flag);
        CASH_HOME_LOGGER.info("getting list cash user personas with value " + value + " and flag is " + flag);
        return new ResponseEntity<>(listUser,HttpStatus.OK);
    }

    @PutMapping("/persona/update/user")
    @CrossOrigin(origins = {"https://cash-management-agendaku-dirty.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://cash-management-agendaku-beta.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://core-agendaku-dirty.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://core-agendaku-beta.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://core-agendaku-south.apps.south.syariahbtpn.com",
            "https://core-agendaku-north.apps.north.syariahbtpn.com",
            "http://localhost:3000", "http://localhost:3001",
            "https://agendaku.apps.btpnsyariah.com"})
    public ResponseEntity updateCashUser(@RequestBody CashUserPersonasUpdate cashUserPersonasUpdate){
        try {
            cashUserPersonasService.updateCashUserPersonas(cashUserPersonasUpdate);
            return new ResponseEntity<>("User data updates successfully",HttpStatus.OK);
        } catch (DataNotFoundException d) {
            cashUserPersonasService.updateCashUserPersonas(cashUserPersonasUpdate);
            CASH_HOME_LOGGER.info("Unable to update user data, data not found with user NIK : {} CoCOde : {}",
                    cashUserPersonasUpdate.getUserData().getNik(),
                    cashUserPersonasUpdate.getUserData().getCoCode());
            return new ResponseEntity<>(d.getMessage(), HttpStatus.NOT_FOUND);
        } catch (Exception e) {
            cashUserPersonasService.updateCashUserPersonas(cashUserPersonasUpdate);
            CASH_HOME_LOGGER.info("Unable to update user data, with user NIK : {} CoCOde : {} Error : {}",
                    cashUserPersonasUpdate.getUserData().getNik(),
                    cashUserPersonasUpdate.getUserData().getCoCode(),
                    e.getMessage());
            return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }

    }

    @PostMapping("/persona/delete/user")
    @CrossOrigin(origins = {"https://cash-management-agendaku-dirty.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://cash-management-agendaku-beta.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://core-agendaku-dirty.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://core-agendaku-beta.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://core-agendaku-south.apps.south.syariahbtpn.com",
            "https://core-agendaku-north.apps.north.syariahbtpn.com",
            "http://localhost:3000", "http://localhost:3001",
            "https://agendaku.apps.btpnsyariah.com"})
    public ResponseEntity deleteCashUserPersonas(@RequestParam String nik, @RequestParam String coCode, HttpServletRequest httpServletRequest){
        String userId = httpServletRequest.getHeader("UserId");

        String userName = httpServletRequest.getHeader("UserName");
        String password = httpServletRequest.getHeader("Password");

        // authorization
        ResponseEntity response = userAuthorizationService.getUserApprovedForUpm(userId, userName, password);
        if (!response.getStatusCode().equals(HttpStatus.OK)) {
            CASH_HOME_LOGGER.info("[CashUserPersonasController][Failed Authorization - delete cash user persona] userId: " +  userId + " authorize by: " + userName);
            return response;
        }

        boolean result = false;
        try{
            result = cashUserPersonasService.deleteCashUserPersonas(nik,coCode);
        }catch (DataNotFoundException e){
            CASH_HOME_LOGGER.error("[CashUserPersonasController] unable to delete cash user personas with exception "+ e.getMessage());
            return new ResponseEntity("Unable to delete cash user personas with exception",HttpStatus.BAD_REQUEST);
        }
        CASH_HOME_LOGGER.info("[CashUserPersonasController] Delete cash user personas successfully with nik "+nik+" and coCode "+coCode+" . executed by : "+userId);
        return new ResponseEntity("Delete Cash User Personas successfully", HttpStatus.OK);
    }

    @PostMapping("/persona/insert/user")
    @CrossOrigin(origins = {"https://cash-management-agendaku-dirty.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://cash-management-agendaku-beta.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://core-agendaku-dirty.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://core-agendaku-beta.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://core-agendaku-south.apps.south.syariahbtpn.com",
            "https://core-agendaku-north.apps.north.syariahbtpn.com",
            "http://localhost:3000", "http://localhost:3001",
            "https://agendaku.apps.btpnsyariah.com"})
    public ResponseEntity insertCashUserPersona(@RequestBody CashUserPersonasEntity userPersona, HttpServletRequest httpServletRequest){
        String userId = httpServletRequest.getHeader("UserId");

        String userName = httpServletRequest.getHeader("UserName");
        String password = httpServletRequest.getHeader("Password");

        // authorization
        ResponseEntity response = userAuthorizationService.getUserApprovedForUpm(userId, userName, password);
        if (!response.getStatusCode().equals(HttpStatus.OK)) {
            CASH_HOME_LOGGER.info("[CashUserPersonasController][Failed Authorization - insert cash user persona] userId: " +  userId + " authorize by: " + userName);
            return response;
        }

        boolean result = false;
        try{
            cashUserPersonasService.saveCashUserPersonas(userPersona);
        }catch (DataNotFoundException e){
            CASH_HOME_LOGGER.error("[CashUserPersonasController] unable to insert cash user personas with exception "+ e.getMessage());
            return new ResponseEntity("Unable to insert cash user personas with exception",HttpStatus.BAD_REQUEST);
        }catch (DatabaseTransactionException e){
            CASH_HOME_LOGGER.error("[CashUserPersonasController] unable to insert cash user personas with exception "+ e.getMessage());
            return new ResponseEntity("Unable to insert cash user personas with exception",HttpStatus.BAD_REQUEST);
        }catch (Exception e){
            CASH_HOME_LOGGER.error("[CashUserPersonasController] Unhandled Exception persona save user with exception" +  e.getMessage());
            return new ResponseEntity("Unhandled Exception causing cash user persona cannot be saved", HttpStatus.INTERNAL_SERVER_ERROR);
        }

        CASH_HOME_LOGGER.info("[CashUserPersonasController] Insert cash user personas successfully with. executed by : "+userId);
        return new ResponseEntity("Insert Cash User Personas successfully", HttpStatus.OK);
    }


    @GetMapping("/persona/assign/report")
    @CrossOrigin(origins = {"https://cash-management-agendaku-dirty.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://cash-management-agendaku-beta.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://core-agendaku-dirty.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://core-agendaku-beta.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://core-agendaku-south.apps.south.syariahbtpn.com",
            "https://core-agendaku-north.apps.north.syariahbtpn.com",
            "http://localhost:3000", "http://localhost:3001",
            "https://agendaku.apps.btpnsyariah.com"})
    public ResponseEntity<List<CashUserAssignModel>> getCashUserAssignReport(HttpServletRequest httpServletRequest){
        String userId = httpServletRequest.getHeader("UserId");
        List<CashUserAssignModel> CashUserAssignModel =  cashUserPersonasService.getCashUserAssignAll();
        CASH_HOME_LOGGER.info("[CashUserPersonasController] Getting report Cash User Assign. executed by : "+userId);
        return new ResponseEntity<>(CashUserAssignModel,HttpStatus.OK);
    }

    @PutMapping("/persona/prospera/sync/{nik}")
    @CrossOrigin(origins = {"https://cash-management-agendaku-dirty.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://cash-management-agendaku-beta.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://core-agendaku-dirty.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://core-agendaku-beta.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://core-agendaku-south.apps.south.syariahbtpn.com",
            "https://core-agendaku-north.apps.north.syariahbtpn.com",
            "http://localhost:3000", "http://localhost:3001",
            "https://agendaku.apps.btpnsyariah.com"})
    public ResponseEntity syncProsperaByNik(@PathVariable String nik){
        try {
            cashUserPersonasService.syncCashUserPersonaToProsperaLombok(nik);
            return new ResponseEntity<>("User sync successfully",HttpStatus.OK);
        } catch (Exception e) {
            CASH_HOME_LOGGER.info("Unable to sync user data, with user NIK : {} Error : {}",
                    nik,
                    e.getMessage());
            return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }

    }

}
