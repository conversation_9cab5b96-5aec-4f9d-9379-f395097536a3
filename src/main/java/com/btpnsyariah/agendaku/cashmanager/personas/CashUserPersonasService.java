package com.btpnsyariah.agendaku.cashmanager.personas;

import com.btpnsyariah.agendaku.cashmanager.config.configcm.ConfigCMEntity;
import com.btpnsyariah.agendaku.cashmanager.config.configcm.ConfigCMRepository;
import com.btpnsyariah.agendaku.cashmanager.config.configcm.ConfigCMService;
import com.btpnsyariah.agendaku.cashmanager.kas.KasHomeMmsModel;
import com.btpnsyariah.agendaku.cashmanager.kas.KasHomeModel;
import com.btpnsyariah.agendaku.cashmanager.kas.OptedPersonaModel;
import com.btpnsyariah.agendaku.cashmanager.kas.menu.HomeScreenMenusEntity;
import com.btpnsyariah.agendaku.cashmanager.kas.menu.HomeScreenMenusRepository;
import com.btpnsyariah.agendaku.cashmanager.model.constant.CmConfigCode;
import com.btpnsyariah.agendaku.cashmanager.model.constant.PersonaRole;
import com.btpnsyariah.agendaku.cashmanager.personas.ActivationReport.ActivationReportEntity;
import com.btpnsyariah.agendaku.cashmanager.personas.ActivationReport.ActivationReportRepository;
import com.btpnsyariah.agendaku.cashmanager.personas.MsBCMappingTUR.MsBCMappingTurRepository;
import com.btpnsyariah.agendaku.cashmanager.report.vehicle.VehicleReportService;
import com.btpnsyariah.agendaku.cashmanager.sprint29.transaction.monthlytransaction.coverdana.CoverDanaMappingRepositoryV2;
import com.btpnsyariah.agendaku.cashmanager.sprint33.transaction.dailytransaction.kwdailytransaction.KWDailyTransactionServiceV5;
import com.btpnsyariah.agendaku.cashmanager.support.services.user.prospera.UserProsperaService;
import com.btpnsyariah.agendaku.cashmanager.transaction.fpb.FPBAgingAlertProjection;
import com.btpnsyariah.agendaku.cashmanager.transaction.fpb.FPBService;
import com.btpnsyariah.agendaku.cashmanager.transaction.monthlytransaction.coverdana.CoverDanaMappingEntity;
import com.btpnsyariah.agendaku.cashmanager.transaction.monthlytransaction.coverdana.CoverDanaMappingRepository;
import com.btpnsyariah.agendaku.cashmanager.transaction.monthlytransaction.coverdana.MmsType;
import com.btpnsyariah.agendaku.cashmanager.transaction.othertransaction.balance.BalanceTransactionRepository;
import com.btpnsyariah.agendaku.cashmanager.transaction.othertransaction.balance.BalanceTransactionService;
import com.btpnsyariah.agendaku.cashmanager.transaction.othertransaction.balance.LbOverlimitGroup;
import com.btpnsyariah.agendaku.cashmanager.transaction.othertransaction.balance.OverLimitWarning;
import com.btpnsyariah.agendaku.cashmanager.transaction.othertransaction.cashopname.CashOpnameTransactionService;
import com.btpnsyariah.agendaku.cashmanager.transaction.othertransaction.overlimit.OverlimitRequestLogRepository;
import com.btpnsyariah.agendaku.cashmanager.uservalidator.UserDetailEntity;
import com.btpnsyariah.agendaku.cashmanager.util.*;
import com.btpnsyariah.agendaku.prospera.model.projection.PersonelProjection;
import com.btpnsyariah.agendaku.prospera.repository.PersonnelRepository;
import javassist.NotFoundException;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static org.springframework.data.jpa.domain.Specification.where;

@Slf4j
@Service
public class CashUserPersonasService {

    private static final Logger USER_PERSONAS_SERVICE_LOGGER = LoggerFactory.getLogger(CashUserPersonasService.class);
    private static final String ROLE_SET = "Y";
    private static final String ROLE_RESET = "N";
    private static final String CO = "CO";
    private static final String BM = "BM";
    private static final String DATE_FORMAT = "yyyy-MM-dd hh:mm:ss.SSS";
    private static final String DATE_FORMAT_INPUT = "yyyyMMdd";
    private static final String KAS_LEMARI_BESI = "KAS_LEMARI_BESI";
    private static final String KAS_CADANGAN = "KAS_CADANGAN";
    private static final String KAS_CADANGAN_TAMBAHAN = "KAS_CADANGAN_TAMBAHAN";
    private static final String KAS_LEMARI_BESI_PENGGANTI = "KAS_LEMARI_BESI_PENGGANTI";
    private static final String KAS_LEMARI_BESI_TAMBAHAN = "KAS_LEMARI_BESI_TAMBAHAN";


    @Autowired
    private CashUserPersonasRepository cashUserPersonasRepository;

    @Autowired
    private HomeScreenMenusRepository homeScreenMenusRepository;

    @Autowired
    private CoverDanaMappingRepository coverDanaMappingRepository;

    @Autowired
    private BalanceTransactionRepository balanceTransactionRepository;

    @Autowired
    private BalanceTransactionService balanceTransactionService;

    @Autowired
    private CashUserPersonasLogRepository cashUserPersonasLogRepository;

    @Autowired
    private OverlimitRequestLogRepository overlimitRequestLogRepository;

    @Autowired
    private KWDailyTransactionServiceV5 kwDailyTransactionService;

    @Autowired
    private ConfigCMRepository configCMRepository;

    @Autowired
    CashOpnameTransactionService cashOpnameTransactionService;

    @Autowired
    private ActivationReportRepository activationReportRepository;

    @Autowired
    private CoverDanaMappingRepositoryV2 coverDanaMappingRepositoryV2;

    @Autowired
    private FPBService fpbService;

    @Autowired
    private ConfigCMService configCMService;

    @Autowired
    PersonnelRepository personnelRepository;

    @Autowired
    UserProsperaService userProsperaService;

    @Autowired
    VehicleReportService vehicleReportService;

    @Autowired
    MsBCMappingTurRepository msBCMappingTurRepository;

    @Autowired
    CashManagementProsperaRoleMapRepository cashManagementProsperaRoleMapRepository;

    @Value("${agendaku.likuiditas.autoposting.toggle}")
    private Set<String> liquidityAutopostingToggle;

    @Value("${agendaku.likuiditas.whitelist.all.toggle}")
    private boolean liquidityWhitelistAllToggle;

    public List<CashUserPersonasEntity> fetchAllUserPersonasForMms(String mmsCode) {
        List<CashUserPersonasEntity> coList = cashUserPersonasRepository.findAllByMmsCodeOrderByCoCodeAsc(mmsCode);
        return coList;
    }

    public List<CashUserPersonasEntity> fetchAllUserPersonasByMms(String mmsCode) {
        return cashUserPersonasRepository.findAllByMmsCodeWithBmOrderByCoCodeAsc(mmsCode);
    }

    public Optional<CashUserPersonasEntity> fetchBMPersonaForMMS(String mmsCode) {
        return cashUserPersonasRepository.findAllByMmsCodeWithBmOrderByCoCodeAsc(mmsCode)
                .stream()
                .filter(e -> e.getPersonaRole().equalsIgnoreCase(BM))
                .findFirst();
    }

    public String fetchAllBMPersonaNameForMMS(String mmsCode) {
        String names = "";
        List<CashUserPersonasEntity> listCashUser = cashUserPersonasRepository.findAllByMmsCodeAndPersonaRole(mmsCode,BM);
        for (int i = 0; i<listCashUser.size(); i++){
            if (i==0){
                names+= listCashUser.get(i).getCoName();
            }else {
                names+= "/"+listCashUser.get(i).getCoName();
            }
        }
        return names;
    }

    public Optional<CashUserPersonasEntity> fetchPersonaByCoCode(String coCode) {
        return cashUserPersonasRepository.findPersonaByCoCode(coCode);
    }

    public void setPilotingToggle(HashMap<String, Boolean> personas) {

        List<CashUserPersonasEntity> cashUserPersonasEntities = cashUserPersonasRepository.findByNiks(new ArrayList<>(personas.keySet()));
        List<CashUserPersonasEntity> newCashUserPersonasEntities = new ArrayList<>();

        for (CashUserPersonasEntity entity : cashUserPersonasEntities) {
            entity.setPilotingToggle(personas.get(entity.getNik()));
            entity.setUpdatedDate(new Date());
            newCashUserPersonasEntities.add(entity);
        }

        cashUserPersonasRepository.saveAll(newCashUserPersonasEntities);
    }

    public Optional<CashUserPersonasEntity> fetchPersonaByNikAndCoCode(String nik, String coCode) {
        return cashUserPersonasRepository.findByNikAndCoCode(nik, coCode);
    }

    public void addLogToActivationReport(ApprovalUser user, String nik, String action, String accountName, Boolean remote){
        List<CashUserPersonasEntity> listCashUserPersonasEntity = new ArrayList<>();
        if(remote) {
            listCashUserPersonasEntity = cashUserPersonasRepository.findByNik(user.getNik());
        } else {
            listCashUserPersonasEntity = cashUserPersonasRepository.findByNik(nik);
        }
        ActivationReportEntity activationReportEntity = new ActivationReportEntity();

        if(!listCashUserPersonasEntity.isEmpty()){
            for (CashUserPersonasEntity persona : listCashUserPersonasEntity){
                Optional<CoverDanaMappingEntity> coverDanaMappingEntity = coverDanaMappingRepository.findCoverDanaByNikAndCoCode(persona.getNik(),persona.getCoCode());
                String kfoCode = coverDanaMappingEntity.get().getKfoCode();

                activationReportEntity.setNik(persona.getNik());
                activationReportEntity.setCoName(persona.getCoName());
                activationReportEntity.setMmsCode(persona.getMmsCode());
                activationReportEntity.setMmsName(persona.getMmsName());
                activationReportEntity.setMobileId(persona.getMobileId());
                activationReportEntity.setCreatedDate(new Timestamp(new Date().getTime()));
                if(remote) {
                    activationReportEntity.setApprovedBy(nik);
                } else {
                    activationReportEntity.setApprovedBy(user.getNik());
                }
                activationReportEntity.setAction(action);
                activationReportEntity.setAccountName(accountName);
                activationReportEntity.setKfoCode(kfoCode);
                activationReportRepository.save(activationReportEntity);
            }
        }
    }

    public List<ActivationReportEntity> getActivationReportByFlag(String flag, String value, String dateFromString, String dateToString){
        Date dateFrom = Utility.convertToDate(dateFromString, new SimpleDateFormat(DATE_FORMAT_INPUT));
        Date dateTo = Utility.convertToDate(dateToString, new SimpleDateFormat(DATE_FORMAT_INPUT));
        List<ActivationReportEntity> activationReportEntities = new ArrayList<>();
        if (flag.equalsIgnoreCase("ALL")){
            activationReportEntities = activationReportRepository.getListActivationReportAll(new Timestamp(dateFrom.getTime()), new Timestamp(dateTo.getTime()));
        }else if(flag.equalsIgnoreCase("MMS")){
            activationReportEntities = activationReportRepository.getListActivationReportByMmsCode(value, new Timestamp(dateFrom.getTime()), new Timestamp(dateTo.getTime()));
        }else if(flag.equalsIgnoreCase("KFO")){
            if(value.charAt(0) == 'K' || value.charAt(0) == 'k'){
                value = value.substring(1);
            }
            activationReportEntities = activationReportRepository.getListActivationReportByKfoCode(value,new Timestamp(dateFrom.getTime()), new Timestamp(dateTo.getTime()));
        }else if(flag.equalsIgnoreCase("NIK")){
            activationReportEntities = activationReportRepository.getListActivationReportByNik(value,new Timestamp(dateFrom.getTime()), new Timestamp(dateTo.getTime()));
        }
        return activationReportEntities;
    }

    public Map<String, List<CashUserPersonasEntity>> fetchAllUserPersonasForNik(String nik,String coCode) throws NotFoundException {
        CashUserPersonasEntity userPersona = getUserPersona(nik, coCode);
        List<CashUserPersonasEntity> allCos;
        if (userPersona.getRoleId() == 42){
            List<String> mmsCodeSBM = msBCMappingTurRepository.findMmsCodeBySBMNik(userPersona.getNik());
            allCos = cashUserPersonasRepository.findAllByMmsCodesOrderByCoCodeAsc(mmsCodeSBM);
        }else {
            allCos = cashUserPersonasRepository.findAllByMmsCodeForGivenNikOrderByCoCodeAsc(nik);
        }


        Map<String, List<CashUserPersonasEntity>> mappedCosToMms = new HashMap<>();
        for (CashUserPersonasEntity eachCo : allCos) {
            List<CashUserPersonasEntity> mappedCos;
            eachCo.setMmsCode(eachCo.getMmsCode().toUpperCase());
            eachCo.setCoCode(eachCo.getCoCode().toUpperCase());
            if (eachCo.getMmsCodeChild() != null && eachCo.getMmsCodeChild() != ""){
                if (!mappedCosToMms.isEmpty() && mappedCosToMms.get(eachCo.getMmsCodeChild()) != null) {
                    mappedCos = mappedCosToMms.get(eachCo.getMmsCodeChild());
                } else {
                    mappedCos = new ArrayList<>();
                }
                mappedCos.add(eachCo);
                mappedCosToMms.put(eachCo.getMmsCodeChild(), mappedCos);
            }else {
                if (!mappedCosToMms.isEmpty() && mappedCosToMms.get(eachCo.getMmsCode()) != null) {
                    mappedCos = mappedCosToMms.get(eachCo.getMmsCode());
                } else {
                    mappedCos = new ArrayList<>();
                }
                mappedCos.add(eachCo);
                mappedCosToMms.put(eachCo.getMmsCode(), mappedCos);
            }
        }
        return mappedCosToMms;
    }

    public String findCoCodeByMMSCodeAndNik(String mmsCode, String nik) throws DatabaseTransactionException {
        Optional<CashUserPersonasEntity> optCUP = fetchAllUserPersonasByMms(mmsCode)
                .stream()
                .parallel()
                .filter(e -> e.getNik().equalsIgnoreCase(nik))
                .findFirst();
        if(optCUP.isPresent()) {
            return optCUP.get().getCoCode();
        } else {
            log.error("Unable to fetch CashUserPersona for NIK {}, mmsCode {}", nik,mmsCode);
            throw new DatabaseTransactionException("Unable to fetch CashUserPersona for NIK " + nik);
        }
    }

    public String findRoleByMMSCodeAndNik(String mmsCode, String nik) throws DatabaseTransactionException {
        Optional<CashUserPersonasEntity> optCUP = fetchAllUserPersonasByMms(mmsCode)
                .stream()
                .parallel()
                .filter(e -> e.getNik().equalsIgnoreCase(nik))
                .findFirst();
        if(optCUP.isPresent()) {
            return optCUP.get().getPersonaRole();
        } else {
            return "";
        }
    }

    @Transactional
    public void updateUserPersonaIfRoleNotSet(UserDetailEntity userDetailEntity) {
        System.out.println("Updated the role to {} for CoCode ={}, Nik={} since it must have been updated at LDAP." + userDetailEntity.getRole() + userDetailEntity.getCoCode() + userDetailEntity.getUserIdentifier());

        if (userDetailEntity.getRole() != null && userDetailEntity.getCoCode() != null && userDetailEntity.getUserIdentifier() != null) {
            int updatedRecord = cashUserPersonasRepository.updateRoleForUser(userDetailEntity.getRole(), userDetailEntity.getCoCode(), userDetailEntity.getUserIdentifier());
            if (updatedRecord > 0) {
                System.out.println("Updated the role to {} for CoCode ={}, Nik={} since it must have been updated at LDAP." + userDetailEntity.getRole() + userDetailEntity.getCoCode() + userDetailEntity.getUserIdentifier());
            }
        }
    }

    @Transactional
    public void updateUserAccessToken(UserDetailEntity userDetail) {
        cashUserPersonasRepository.updateAccessTokenForUser(userDetail.getAccessToken(), userDetail.getCoCode(), userDetail.getUserIdentifier());
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW, isolation = Isolation.READ_UNCOMMITTED, rollbackFor = {DatabaseTransactionException.class, BalanceException.class})
    public void saveAllUserPersonasForMms(List<CashUserPersonasEntity> userPersonas, String userId) throws DatabaseTransactionException, Exception{
        List<CashUserPersonasEntity> oldPersonas = new ArrayList<>();
        boolean roleSet = false, roleReset = false;
        List<String> mmsCodes = new ArrayList<>();

        for (CashUserPersonasEntity userPersonasEntity : userPersonas) {
            kwDailyTransactionService.persistUnsignKasTambahan(userPersonasEntity.getMmsCode(),userPersonasEntity.getNik(),userPersonasEntity.getCoCode());
            userPersonasEntity.setUpdatedDate(new Date());
            if (userPersonasEntity.getPersonaRole().equalsIgnoreCase(CO)) {
                userPersonasEntity.setIsRoleSet(ROLE_RESET);
                roleReset = true;
            } else {
                userPersonasEntity.setIsRoleSet(ROLE_SET);
                roleSet = true;
            }
            Optional<CashUserPersonasEntity> optCup = fetchPersonaByNikAndCoCode(userPersonasEntity.getNik(),userPersonasEntity.getCoCode());
            if(optCup.isPresent()) {
                oldPersonas.add(optCup.get());
                userPersonasEntity.setMmsCode(optCup.get().getMmsCode());
            }

            if(userPersonasEntity.getPersonaRole().equals(KAS_LEMARI_BESI)){
                mmsCodes.add(userPersonasEntity.getMmsCode());
            }
        }

        if(mmsCodes.size() > 0){
            Calendar now = Calendar.getInstance();
//            now.set(Calendar.HOUR, 0);
//            now.set(Calendar.MINUTE, 0);
//            now.set(Calendar.SECOND, 0);
//            now.set(Calendar.HOUR_OF_DAY, 0);
//            now.set(Calendar.MILLISECOND, 0);

            List<CoverDanaMappingEntity> cdms = coverDanaMappingRepositoryV2.findByMmsCode(mmsCodes);
            cdms.stream().forEach(cd -> cd.setLatestPicLembesChangeUpdated(now.getTime()));
            coverDanaMappingRepositoryV2.saveAll(cdms);
        }

        // Looping for newly changed user personas role
        List<CashUserPersonasLogEntity> cupLogs = userPersonas.stream().map(e -> {
            CashUserPersonasLogEntity cupl = new CashUserPersonasLogEntity();
            cupl.setMmsCode(e.getMmsCode());
            cupl.setPersonaRole(e.getPersonaRole());
            cupl.setCreatedBy(userId);
            cupl.setCreatedByName(getCoNameByNik(userId));
            cupl.setCreatedDate(new Timestamp(new Date().getTime()));
            cupl.setNewUser(e.getNik());
            cupl.setNewUserName(getCoNameByNik(e.getNik()));
            return cupl;
        }).collect(Collectors.toList());

        // Looping for old user personas role
        if(roleReset && roleSet) {
            cupLogs = cupLogs.stream().map(e -> {
                Optional<CashUserPersonasEntity> opt = oldPersonas
                        .stream()
                        .filter(x -> x.getPersonaRole().equalsIgnoreCase(e.getPersonaRole()))
                        .findFirst();
                if(opt.isPresent() && !opt.get().getPersonaRole().equalsIgnoreCase(CO)) {
                        e.setOldUser(opt.get().getNik());
                        e.setOldUserName(getCoNameByNik(opt.get().getNik()));
                        return e;
                } else if (!opt.isPresent() && (e.getPersonaRole().equalsIgnoreCase(KAS_CADANGAN) || e.getPersonaRole().equalsIgnoreCase(KAS_LEMARI_BESI))) {
                    return e;
                }

                return new CashUserPersonasLogEntity();
            }).collect(Collectors.toList()).stream()
                    .filter(e ->  e.getPersonaRole() != null)
                    .collect(Collectors.toList());
        }

        cashUserPersonasRepository.saveAll(userPersonas);
        cashUserPersonasLogRepository.saveAll(cupLogs);
        USER_PERSONAS_SERVICE_LOGGER.info("Saved all the personas into database");
    }

    public List<CashUserLog> fetchAllLogsOfUserChanges(String mmsCode) {
        List<CashUserPersonasLogEntity> culeList = cashUserPersonasLogRepository.findAllByMMSCodeOrderByCreatedDateDesc(mmsCode);
        List<CashUserLog> culList = new ArrayList<>();

        for (CashUserPersonasLogEntity e : culeList) {
            if(!e.getPersonaRole().equals(CO)){
                CashUserLog cul = new CashUserLog();
                cul.setMmsCode(e.getMmsCode());
                cul.setPersonaRole(e.getPersonaRole());
                cul.setNewUser(e.getNewUser());
                cul.setNewUserName(e.getNewUserName());
                if(cul.getNewUserName() == null){
                    cul.setNewUserName(getCoNameByNik(e.getNewUser()));
                }
                cul.setOldUser(e.getOldUser());
                cul.setOldUserName(e.getOldUserName());
                if(cul.getOldUserName() == null){
                    cul.setOldUserName(getCoNameByNik(e.getOldUser()));
                }
                cul.setCreatedBy(e.getCreatedBy());
                cul.setCreatedByName(e.getCreatedByName());
                if(cul.getCreatedByName() == null){
                    cul.setCreatedByName(getCoNameByNik(e.getCreatedBy()));
                }
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(e.getCreatedDate());
                calendar.add(Calendar.HOUR_OF_DAY, 7);

                cul.setCreatedDate(new Timestamp(calendar.getTimeInMillis()));
                culList.add(cul);
            }
        }

        return culList;
    }

    public List<CashUserLog> fetchAllLogsOfUserChangesByDateRange(String mmsCode, String dateFromString, String dateToString){
        Date dateFrom = Utility.convertToDate(dateFromString, new SimpleDateFormat(DATE_FORMAT_INPUT));
        Date dateTo = Utility.convertToDate(dateToString, new SimpleDateFormat(DATE_FORMAT_INPUT));
        List<CashUserPersonasLogEntity> culeList = cashUserPersonasLogRepository.findAllByMMSCodeOrderByDateRangeCreatedDateDesc(mmsCode,new Timestamp(dateFrom.getTime()), new Timestamp(dateTo.getTime()));
        List<CashUserLog> culList = new ArrayList<>();

        for (CashUserPersonasLogEntity e : culeList) {
            if(!e.getPersonaRole().equals(CO)){
                CashUserLog cul = new CashUserLog();
                cul.setMmsCode(e.getMmsCode());
                cul.setPersonaRole(e.getPersonaRole());
                cul.setNewUser(e.getNewUser());
                cul.setNewUserName(e.getNewUserName());
                if(cul.getNewUserName() == null){
                    cul.setNewUserName(getCoNameByNik(e.getNewUser()));
                }
                cul.setOldUser(e.getOldUser());
                cul.setOldUserName(e.getOldUserName());
                if(cul.getOldUserName() == null){
                    cul.setOldUserName(getCoNameByNik(e.getOldUser()));
                }
                cul.setCreatedBy(e.getCreatedBy());
                cul.setCreatedByName(e.getCreatedByName());
                if(cul.getCreatedByName() == null){
                    cul.setCreatedByName(getCoNameByNik(e.getCreatedBy()));
                }
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(e.getCreatedDate());
                calendar.add(Calendar.HOUR_OF_DAY, 7);

                cul.setCreatedDate(new Timestamp(calendar.getTimeInMillis()));
                culList.add(cul);
            }
        }

        return culList;
    }

    public KasHomeModel fetchKasHomeDetailsQueries(String nik, String cocode, String mmsCode, String appVersion) throws NotFoundException {
        // fetch menus for NIK
        List<HomeScreenMenusEntity> homeMenus = homeScreenMenusRepository.findMenusForNik(nik);

        if(!Utility.isApkVersionEqualOrBiggerThan(appVersion,"1.104.0")) {
            homeMenus = homeMenus.stream().filter(h -> !h.getMenuCode().toLowerCase().contains("insurance")).collect(Collectors.toList());
        }

        KasHomeModel kasHomeModel = new KasHomeModel();

        ConfigCMEntity config =configCMRepository.getOne(CmConfigCode.IDTM);
        if (config != null){
            kasHomeModel.setIdleTime( Long.parseLong(config.getValue()));
        }

        Optional<CashUserPersonasEntity> cashUserPersonasEntityOptional = cashUserPersonasRepository.findByNikAndCoCode(nik,cocode);
        if (!cashUserPersonasEntityOptional.isPresent()) {
            throw new NotFoundException("User notfound withh nik and cocoode :"+nik+" "+cocode);
        }
        CashUserPersonasEntity cashUserPersonasEntity=cashUserPersonasEntityOptional.get();
        kasHomeModel.setRole(cashUserPersonasEntity.getPersonaRole());
        Optional<ActivationReportEntity> optionalActReportEntity = activationReportRepository.getTopOneReportByNikAndMmsCode(nik,cashUserPersonasEntity.getMmsCode());

        if (optionalActReportEntity.isPresent() && !optionalActReportEntity.get().getAction().equalsIgnoreCase("RESET")){
            kasHomeModel.setMobileId(optionalActReportEntity.get().getMobileId());
        }

        //add MENU_COVER_DANA if CoverDanaLB menu is active and Persona Role is KAS_LEMARI_BESI
        Optional<CoverDanaMappingEntity> coverDanaMappingEntity = coverDanaMappingRepository.findCoverDanaByNikAndCoCode(nik,cocode);
        boolean isActive = coverDanaMappingEntity.get().isCoverDanaLbMenu();
        String role = kasHomeModel.getRole();
        if(isActive && (role.equalsIgnoreCase(KAS_LEMARI_BESI) || role.equalsIgnoreCase(KAS_LEMARI_BESI_PENGGANTI) || role.equalsIgnoreCase(KAS_LEMARI_BESI_TAMBAHAN))){
            Optional<HomeScreenMenusEntity> menuCoverDana  = homeScreenMenusRepository.findMenuByMenuCode("MENU_COVER_DANA");
            menuCoverDana.get().setPersonaRole(role);
            menuCoverDana.get().setMenuAction("SHOW");
            homeMenus.add(0,menuCoverDana.get());
        }

        kasHomeModel.setMenus(homeMenus);

        Map<String, KasHomeMmsModel> mappedMmsDetails = new HashMap<>();

        List<CashUserPersonasEntity> personas = cashUserPersonasRepository.findPersonasForMmsCodesOfNik(nik);
        kasHomeModel.setCoCollection(cashUserPersonasEntity.getPersonaRole().equalsIgnoreCase("CO") && personas.size() > 1);

        for (CashUserPersonasEntity personasEntity : personas) {
            List<CashUserPersonasEntity> listOfPersonas;
            personasEntity.setMmsCode(personasEntity.getMmsCode().toUpperCase());
            personasEntity.setCoCode(personasEntity.getCoCode().toUpperCase());
            if (mappedMmsDetails.isEmpty() || mappedMmsDetails.get(personasEntity.getMmsCode()) == null) {
                listOfPersonas = new ArrayList<>();
            } else {
                listOfPersonas = mappedMmsDetails.get(personasEntity.getMmsCode()).getUserPersonas();
            }
            listOfPersonas.add(personasEntity);
            KasHomeMmsModel kasHomeMmsModel = new KasHomeMmsModel();
            kasHomeMmsModel.setUserPersonas(listOfPersonas);
            mappedMmsDetails.put(personasEntity.getMmsCode(), kasHomeMmsModel);
        }

        List<Object[]> kasHomeMmsModelList = new ArrayList<>();
        if (kasHomeModel.getRole().equalsIgnoreCase(BM)){
            kasHomeMmsModelList = cashUserPersonasRepository.findMmsRelatedDetailsByNikForBM(nik);
        }else {
            kasHomeMmsModelList = cashUserPersonasRepository.findMmsRelatedDetailsByNik(nik);
        }

        for (Object[] kasHomeModelObject : kasHomeMmsModelList){
            KasHomeMmsModel kasHomeMmsModel;
            if (mappedMmsDetails.isEmpty() || mappedMmsDetails.get(kasHomeModelObject[0].toString()) == null) {
                kasHomeMmsModel = new KasHomeMmsModel();
            } else {
                kasHomeMmsModel = mappedMmsDetails.get(kasHomeModelObject[0].toString());
            }

            if (mappedMmsDetails.get((String) kasHomeModelObject[9]) != null){
                kasHomeMmsModel.setUserPersonas(mappedMmsDetails.get((String) kasHomeModelObject[9]).getUserPersonas());
            }else {
                kasHomeMmsModel.setUserPersonas(new ArrayList<>());
            }

            kasHomeMmsModel.setMmsName(kasHomeModelObject[1].toString());
            kasHomeMmsModel.setMprosEstimation((BigDecimal) kasHomeModelObject[2]);
            kasHomeMmsModel.setCashManagementAuthorized((Boolean) kasHomeModelObject[3]);
            kasHomeMmsModel.setLemariBesiBalance((BigDecimal) kasHomeModelObject[4]);
            kasHomeMmsModel.setKasWismaBalance((BigDecimal) kasHomeModelObject[5]);

            CoverDanaMappingEntity cdEntity = coverDanaMappingRepositoryV2.findByMmsCode(kasHomeModelObject[0].toString());
            kasHomeMmsModel.setKfoCode(cdEntity.getKfoCode());
            kasHomeMmsModel.setKfoName(cdEntity.getKfoName());

            kasHomeMmsModel.setMmsType(MmsType.valueOf( kasHomeModelObject[7].toString()));
            kasHomeMmsModel.setMmsCodeChild((String) kasHomeModelObject[8]);
            kasHomeMmsModel.setMmsCodeParent((String) kasHomeModelObject[9]);
            kasHomeMmsModel.setCoverDanaSchedule(new Date());
            kasHomeMmsModel.setUseJournalPosting((Boolean) kasHomeModelObject[10]);
            kasHomeMmsModel.setCashOpnameExpired(cashOpnameTransactionService.isCashopnameExpired(kasHomeModelObject[0].toString()));
            kasHomeMmsModel.setUseCashOpname((Boolean) kasHomeModelObject[11]);
            kasHomeMmsModel.setMmsNameChild((String) kasHomeModelObject[12]);
            kasHomeMmsModel.setPiloting((Boolean) kasHomeModelObject[13]);

            List<CashUserPersonaAssignProjection> cashUserPersonaAssignProjections = new ArrayList<>();
            cashUserPersonasLogRepository.findLastAssignedRole(kasHomeModelObject[0].toString(),KAS_LEMARI_BESI).ifPresent(cashUserPersonaAssignProjections::add);
            cashUserPersonasLogRepository.findLastAssignedRole(kasHomeModelObject[0].toString(),KAS_CADANGAN).ifPresent(cashUserPersonaAssignProjections::add);

            kasHomeMmsModel.getUserPersonas().stream().filter(e->e.getPersonaRole().equalsIgnoreCase(KAS_CADANGAN_TAMBAHAN)).forEach(e->{
                cashUserPersonasLogRepository.findLastAssignedRoleByNik(kasHomeModelObject[0].toString(),KAS_CADANGAN_TAMBAHAN,e.getNik()).ifPresent(cashUserPersonaAssignProjections::add);
            });

            kasHomeMmsModel.setCashUserPersonaAssigns(cashUserPersonaAssignProjections);
            kasHomeMmsModel.setLbOverlimitGroups(new ArrayList<>());

            kasHomeMmsModel.setLbLimit(cdEntity.getLbLimit());
            if (cdEntity.getLimitGroupId() == null){
                kasHomeMmsModel.setWarningLimitLB((Boolean) kasHomeModelObject[6]);
            }else {
                List<Object[]> objects = balanceTransactionRepository.findAllOverlimitGrroupByMmsCode((String) kasHomeModelObject[0]);
                List<LbOverlimitGroup> lbOverlimitGroups= objects.stream().map(x-> LbOverlimitGroup.builder()
                        .mmsCode((String) x[0])
                        .mmsName((String) x[1])
                        .lbAmount((BigDecimal) x[2])
                        .build()).collect(Collectors.toList());
                BigDecimal lbAmountTotal = lbOverlimitGroups.stream().map(LbOverlimitGroup::getLbAmount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add).add(kasHomeMmsModel.getLemariBesiBalance());
                boolean warningOverlimit = lbAmountTotal.compareTo(cdEntity.getLbLimit()) > 0;
                kasHomeMmsModel.setWarningLimitLB(warningOverlimit);
                kasHomeMmsModel.setLbOverlimitGroups(lbOverlimitGroups);
            }
            if (kasHomeMmsModel.isWarningLimitLB()){
                kasHomeMmsModel.setOverlimitAlert(balanceTransactionService.isOverlimitReported(kasHomeModelObject[0].toString()));
            }else {
                kasHomeMmsModel.setOverlimitAlert(OverLimitWarning.NOT_OVERLIMIT);
            }
            kasHomeMmsModel.setNoKomChangesDates(coverDanaMappingRepositoryV2.fetchLatestPicLbChangeDate((String) kasHomeModelObject[9]));
            mappedMmsDetails.put(kasHomeModelObject[0].toString(), kasHomeMmsModel);
        }



        final Map<String, List<OptedPersonaModel>> optedPersonaModels = getCoverDanaOptedPersonaByMmsCodesOfNik(nik);

        if (!optedPersonaModels.isEmpty()) {
            for (Map.Entry<String, KasHomeMmsModel> eachMmsDetails : mappedMmsDetails.entrySet()) {
                KasHomeMmsModel mmsDetails = eachMmsDetails.getValue();
                mmsDetails.setOptedCoDetails(optedPersonaModels.get(eachMmsDetails.getKey()) != null ? optedPersonaModels.get(eachMmsDetails.getKey()) : Collections.EMPTY_LIST);
            }
        }

        kasHomeModel.setMappedMmsDetails(mappedMmsDetails);
        if(liquidityWhitelistAllToggle) {
            kasHomeModel.setAutopostingToggle(true);
        } else {
            kasHomeModel.setAutopostingToggle(liquidityAutopostingToggle != null && liquidityAutopostingToggle.contains(mmsCode));
        }

        return kasHomeModel;
    }

    public KasHomeModel fetchKasHomeDetails(String nik, String cocode, String mmsCode, String appVersion) throws NotFoundException {
        // Inisialisasi model
        KasHomeModel kasHomeModel = new KasHomeModel();


        //set vehicle scan period
        kasHomeModel.setScanVehiclePeriod(vehicleReportService.getScanVehiclePeriod(nik,kasHomeModel));

        // Validasi user dan ambil informasi dasar
        CashUserPersonasEntity userPersona = getUserPersona(nik, cocode);
        kasHomeModel.setRole(userPersona.getPersonaRole());
        kasHomeModel.setRoleId(userPersona.getRoleId());

        boolean isCollection = isCoCollection(userPersona.getPersonaRole(),nik);
        kasHomeModel.setCoCollection(isCollection);
        // Set Menus
        List<HomeScreenMenusEntity> homeMenus = getHomeMenus(nik, cocode, kasHomeModel.getRole(),isCollection, appVersion);
        kasHomeModel.setMenus(homeMenus);

        // Set Idle Time
        setIdleTime(kasHomeModel);


        // Set toggle Autoposting
        setAutopostingToggle(kasHomeModel, mmsCode);


        // Ambil detail MMS dan personas terkait
        Map<String, KasHomeMmsModel> mappedMmsDetails = buildMmsDetails(nik, kasHomeModel, userPersona);

        // Tambahkan informasi Opted Persona
        setOptedPersonaDetails(nik, mappedMmsDetails);


        kasHomeModel.setMappedMmsDetails(mappedMmsDetails);
        return kasHomeModel;
    }



    private void setIdleTime(KasHomeModel kasHomeModel) {
        ConfigCMEntity config = configCMRepository.getOne(CmConfigCode.IDTM);
        if (config != null) {
            kasHomeModel.setIdleTime(Long.parseLong(config.getValue()));
        }
    }

    private CashUserPersonasEntity getUserPersona(String nik, String cocode) throws NotFoundException {
        return cashUserPersonasRepository.findByNikAndCoCode(nik, cocode)
                .orElseThrow(() -> new NotFoundException("User not found with nik and cocode: " + nik + " " + cocode));
    }

    public boolean isRoleSBM(String nik, String coCode) throws NotFoundException {
         return getUserPersona(nik,coCode).getRoleId() == 42;
    }

    private List<HomeScreenMenusEntity> getHomeMenus(String nik, String cocode, String role, boolean isCollection, String appVersion) {
        List<HomeScreenMenusEntity> homeMenus = getMappedHomeMenus(isCollection, nik, appVersion);
        coverDanaMappingRepository.findCoverDanaByNikAndCoCode(nik, cocode)
                .filter(CoverDanaMappingEntity::isCoverDanaLbMenu)
                .filter(mapping -> isKasLemariBesiRole(role))
                .flatMap(mapping -> homeScreenMenusRepository.findMenuByMenuCode("MENU_COVER_DANA"))
                .ifPresent(menu -> {
                    menu.setPersonaRole(role);
                    menu.setMenuAction("SHOW");
                    homeMenus.add(0, menu);
                });

        return homeMenus;
    }

    private List<HomeScreenMenusEntity> getMappedHomeMenus(boolean isCollection, String nik, String appVersion) {
        List<HomeScreenMenusEntity> homeMenus;
        if(isCollection) {
            homeMenus = homeScreenMenusRepository.findMenusForCoCs();
        }else {
            homeMenus = homeScreenMenusRepository.findMenusForNik(nik);
        }

        if(!Utility.isApkVersionEqualOrBiggerThan(appVersion,"1.104.0")) {
            homeMenus = homeMenus.stream().filter(h -> !h.getMenuCode().toLowerCase().contains("insurance")).collect(Collectors.toList());
        }

        if(!Utility.isApkVersionEqualOrBiggerThan(appVersion,"1.108.0")) {
            homeMenus = homeMenus.stream().filter(h -> !h.getMenuCode().equalsIgnoreCase("MENU_SCAN_QR_KENDARAAN") && !h.getMenuCode().equalsIgnoreCase("MENU_KONTIGENSI_PULANG_PRS")).collect(Collectors.toList());
        }

        return homeMenus;
    }

    private boolean isCoCollection(String role, String nik) {
        List<CashUserPersonasEntity> personas = cashUserPersonasRepository.findPersonasForNik(nik);
        return CO.equalsIgnoreCase(role) && personas.size() > 1;
    }

    private boolean isKasLemariBesiRole(String role) {
        return Arrays.asList(KAS_LEMARI_BESI, KAS_LEMARI_BESI_PENGGANTI, KAS_LEMARI_BESI_TAMBAHAN)
                .stream()
                .anyMatch(role::equalsIgnoreCase);
    }

    private Map<String, KasHomeMmsModel> buildMmsDetails(String nik, KasHomeModel kasHomeModel, CashUserPersonasEntity userPersona) throws NotFoundException {
        Map<String, KasHomeMmsModel> mappedMmsDetails = new HashMap<>();
        List<CashUserPersonasEntity> personas;
        List<String> mmsCodeSBM = null;
        if (userPersona.getRoleId() == 42){
            mmsCodeSBM = msBCMappingTurRepository.findMmsCodeBySBMNik(userPersona.getNik());
            if (mmsCodeSBM.isEmpty()){
                throw new NotFoundException("SBM data notfound with nik "+nik);
            }
            personas = cashUserPersonasRepository.findPersonasByMmsCodeList(mmsCodeSBM);

        }else {
            personas = cashUserPersonasRepository.findPersonasForMmsCodesOfNik(nik);
        }

        for (CashUserPersonasEntity persona : personas) {
            persona.setMmsCode(persona.getMmsCode().toUpperCase());
            persona.setCoCode(persona.getCoCode().toUpperCase());
            persona.setFcmToken("");
            persona.setAccessToken("");

            KasHomeMmsModel kasHomeMmsModel = mappedMmsDetails
                    .computeIfAbsent(persona.getMmsCode(), key -> {
                        KasHomeMmsModel kasHomeMmsModelNew = new KasHomeMmsModel();
                        kasHomeMmsModelNew.setUserPersonas(new ArrayList<>());
                        return kasHomeMmsModelNew;
                    });
            kasHomeMmsModel.getUserPersonas().add(persona);
        }

        List<Object[]> mmsDetails = fetchObjectMmsDetails(kasHomeModel.getRole(),nik,userPersona.getRoleId(),mmsCodeSBM);
        populateMmsModelDetails(mappedMmsDetails,mmsDetails);
        return mappedMmsDetails;
    }
    private List<Object[]> fetchObjectMmsDetails(String role, String nik, int roleId,List<String> mmsCodeSBM) {
        if (roleId == 42) {
            return cashUserPersonasRepository.findMmsRelatedDetailsByNikForBMHO(mmsCodeSBM);
        }else if (role.equalsIgnoreCase(BM)) {
            return cashUserPersonasRepository.findMmsRelatedDetailsByNikForBM(nik);
        }
        return cashUserPersonasRepository.findMmsRelatedDetailsByNik(nik);
    }

    private void populateMmsModelDetails(Map<String, KasHomeMmsModel> mappedMmsDetails, List<Object[]> mmsDetails) {

        for (Object[] details : mmsDetails) {
            String mmsCode = details[0].toString();
            String mmsParentCode = (String) details[9];
            KasHomeMmsModel model;
            if (mappedMmsDetails.isEmpty() || mappedMmsDetails.get(mmsCode) == null) {
                model = new KasHomeMmsModel();
            } else {
                model = mappedMmsDetails.get(mmsCode);
            }

            KasHomeMmsModel parentModel = mappedMmsDetails.get(mmsParentCode);
            model.setUserPersonas(
                    parentModel != null ? parentModel.getUserPersonas() : new ArrayList<>()
            );

            model.setMmsName(details[1].toString());
            model.setMprosEstimation((BigDecimal) details[2]);
            model.setCashManagementAuthorized((Boolean) details[3]);
            model.setLemariBesiBalance((BigDecimal) details[4]);
            model.setKasWismaBalance((BigDecimal) details[5]);

            model.setMmsType(MmsType.valueOf( details[7].toString()));
            model.setMmsCodeChild((String) details[8]);
            model.setMmsCodeParent((String) details[9]);
            model.setCoverDanaSchedule(new Date());
            model.setUseJournalPosting((Boolean) details[10]);
            model.setCashOpnameExpired(cashOpnameTransactionService.isCashopnameExpired(mmsCode));
            model.setUseCashOpname((Boolean) details[11]);
            model.setMmsNameChild((String) details[12]);
            model.setPiloting((Boolean) details[13]);

            model.setNoKomChangesDates(coverDanaMappingRepositoryV2.fetchLatestPicLbChangeDate((String) details[9]));

            model.setCashUserPersonaAssigns(getCashUserPersonaAssignProjections(model,mmsCode));
            model.setLbOverlimitGroups(new ArrayList<>());

            CoverDanaMappingEntity cdEntity = coverDanaMappingRepositoryV2.findByMmsCode(mmsCode);
            model.setKfoCode(cdEntity.getKfoCode());
            model.setKfoName(cdEntity.getKfoName());
            model.setLbLimit(cdEntity.getLbLimit());
            populateCoverDanaDetails(model, cdEntity, details);

            // Calculate overlimit groups and warnings
            calculateOverlimitDetails(model, cdEntity, details);
            mappedMmsDetails.put(mmsCode,model);
        }
    }

    private List<CashUserPersonaAssignProjection> getCashUserPersonaAssignProjections(KasHomeMmsModel model,String mmsCode){
        List<CashUserPersonaAssignProjection> cashUserPersonaAssignProjections = new ArrayList<>();
        cashUserPersonasLogRepository.findLastAssignedRole(mmsCode,KAS_LEMARI_BESI).ifPresent(cashUserPersonaAssignProjections::add);
        cashUserPersonasLogRepository.findLastAssignedRole(mmsCode,KAS_CADANGAN).ifPresent(cashUserPersonaAssignProjections::add);

        model.getUserPersonas().stream().filter(e->e.getPersonaRole().equalsIgnoreCase(KAS_CADANGAN_TAMBAHAN)).forEach(e->{
            cashUserPersonasLogRepository.findLastAssignedRoleByNik(mmsCode,KAS_CADANGAN_TAMBAHAN,e.getNik()).ifPresent(cashUserPersonaAssignProjections::add);
        });
        return cashUserPersonaAssignProjections;
    }

    private void populateCoverDanaDetails(KasHomeMmsModel model, CoverDanaMappingEntity cdEntity, Object[] details) {
        model.setKfoCode(cdEntity.getKfoCode());
        model.setKfoName(cdEntity.getKfoName());
        model.setLbLimit(cdEntity.getLbLimit());
        model.setWarningLimitLB(cdEntity.getLimitGroupId() == null ? (Boolean) details[6] : checkOverlimit(cdEntity, model));
    }

    private boolean checkOverlimit(CoverDanaMappingEntity cdEntity, KasHomeMmsModel model) {
        List<Object[]> groups = balanceTransactionRepository.findAllOverlimitGrroupByMmsCode(cdEntity.getMmsCode());
        BigDecimal totalLbAmount = groups.stream()
                .map(group -> (BigDecimal) group[2])
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .add(model.getLemariBesiBalance());

        return totalLbAmount.compareTo(cdEntity.getLbLimit()) > 0;
    }

    private void setOptedPersonaDetails(String nik, Map<String, KasHomeMmsModel> mappedMmsDetails) {
        Map<String, List<OptedPersonaModel>> optedPersonas = getCoverDanaOptedPersonaByMmsCodesOfNik(nik);
        optedPersonas.forEach((key, value) -> {
            KasHomeMmsModel model = mappedMmsDetails.get(key);
            if (model != null) {
                model.setOptedCoDetails(value != null ? value : Collections.emptyList());
            }
        });
    }

    private void setAutopostingToggle(KasHomeModel kasHomeModel, String mmsCode) {
        kasHomeModel.setAutopostingToggle(
                liquidityWhitelistAllToggle || (liquidityAutopostingToggle != null && liquidityAutopostingToggle.contains(mmsCode))
        );
    }

    private void calculateOverlimitDetails(KasHomeMmsModel mmsModel, CoverDanaMappingEntity cdEntity, Object[] model) {
        List<LbOverlimitGroup> lbOverlimitGroups = new ArrayList<>();

        if (cdEntity.getLimitGroupId() != null) {
            List<Object[]> overlimitGroups = balanceTransactionRepository.findAllOverlimitGrroupByMmsCode((String) model[0]);
            lbOverlimitGroups = overlimitGroups.stream()
                    .map(group -> new LbOverlimitGroup((String) group[0], (String) group[1], (BigDecimal) group[2]))
                    .collect(Collectors.toList());

            BigDecimal totalAmount = lbOverlimitGroups.stream()
                    .map(LbOverlimitGroup::getLbAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                    .add(mmsModel.getLemariBesiBalance());

            mmsModel.setWarningLimitLB(totalAmount.compareTo(cdEntity.getLbLimit()) > 0);
        } else {
            mmsModel.setWarningLimitLB((Boolean) model[6]);
        }

        mmsModel.setLbOverlimitGroups(lbOverlimitGroups);
        if (mmsModel.isWarningLimitLB()) {
            mmsModel.setOverlimitAlert(balanceTransactionService.isOverlimitReported(model[0].toString()));
        } else {
            mmsModel.setOverlimitAlert(OverLimitWarning.NOT_OVERLIMIT);
        }
    }



    private Map<String, List<OptedPersonaModel>> getCoverDanaOptedPersonaByMmsCodesOfNik(final String nik) {
        final Map<Integer, OptedPersonaModel> optedPersonaModelMap = new HashMap<>();
        final Map<String, List<OptedPersonaModel>> mappedOptedPersonaAsPerMmsCode = new HashMap<>();
        final List<Object[]> personaList = coverDanaMappingRepository.getPersonaByMmsCodesBelongingToGivenNik(nik);
        int index = 0;
        for (Object[] persona : personaList) {
            OptedPersonaModel optedPersonaModel;

            final Integer id = ((BigInteger) persona[0]).intValue();
            if (optedPersonaModelMap.isEmpty() || optedPersonaModelMap.get(id) == null) {
                optedPersonaModel = new OptedPersonaModel();
                optedPersonaModel.setFirstNik((String) persona[1]);
                optedPersonaModel.setFirstCoCode((String) persona[2]);
                optedPersonaModel.setFirstCoName((String) persona[3]);
                optedPersonaModel.setRequestedFor((String) persona[4]);
                optedPersonaModel.setRequestAmount((BigDecimal) persona[5]);
                optedPersonaModel.setMmsCode((String) persona[6]);
                optedPersonaModel.setIndex(index++);
                optedPersonaModelMap.put(id, optedPersonaModel);
            } else {
                // add the second opted Co to the same Persona Model
                optedPersonaModel = optedPersonaModelMap.get(id);
                optedPersonaModel.setSecondNik((String) persona[1]);
                optedPersonaModel.setSecondCoCode((String) persona[2]);
                optedPersonaModel.setSecondCoName((String) persona[3]);
            }
        }
        // iterate over different persona model and categorize based on MMS Code
        for (Map.Entry<Integer, OptedPersonaModel> eachOptedPersonaModel : optedPersonaModelMap.entrySet()) {
            List<OptedPersonaModel> optedPersonasForMms;
            String mmsCode = eachOptedPersonaModel.getValue().getMmsCode();
            if (mmsCode != null) {
                if (mappedOptedPersonaAsPerMmsCode.isEmpty() || mappedOptedPersonaAsPerMmsCode.get(mmsCode) == null) {
                    optedPersonasForMms = new ArrayList<>();
                } else {
                    optedPersonasForMms = mappedOptedPersonaAsPerMmsCode.get(mmsCode);
                }
                optedPersonasForMms.add(eachOptedPersonaModel.getValue());
                mappedOptedPersonaAsPerMmsCode.put(mmsCode, optedPersonasForMms.stream().sorted(Comparator.comparing(e -> e.getIndex())).collect(Collectors.toList()));
            }
        }
        return mappedOptedPersonaAsPerMmsCode;
    }

    public List<CashUserPersonasDto> fetchAllPersonaBymmsCodeAndRole(String mmsCode, String role){
        List<CashUserPersonasEntity> entities = cashUserPersonasRepository.findAllByMmsCodeAndPersonaRole(mmsCode, role);
        return entities.stream().map(this::convertToDto).collect(Collectors.toList());
    }

    private CashUserPersonasDto convertToDto(CashUserPersonasEntity entity) {
        return CashUserPersonasDto.builder()
                .mmsCode(entity.getMmsCode())
                .mmsName(entity.getMmsName())
                .nik(entity.getNik())
                .coCode(entity.getCoCode())
                .coName(entity.getCoName())
                .coMail(entity.getCoMail())
                .personaRole(entity.getPersonaRole())
                .isRoleSet(entity.getIsRoleSet())
                .updatedDate(entity.getUpdatedDate())
                .mmsCodeChild(entity.getMmsCodeChild())
                .mmsNameChild(entity.getMmsNameChild())
                .mobileId(entity.getMobileId())
                .fcmToken(entity.getFcmToken())
                .accessToken(entity.getAccessToken())
                .pilotingToggle(entity.getPilotingToggle())
                .roleId(entity.getRoleId())
                .build();
    }

    public List<CashUserPersonasEntity> fetchAllPersonaBymmsCodeAndRoles(String mmsCode, List<String> roles){
        return cashUserPersonasRepository.findByMmsCodeAndPersonaRoleIn(mmsCode,roles);
    }

    @Transactional(readOnly = true)
    public boolean existsByNikAndPersonaRole(String nik, String personaRole) {
        return cashUserPersonasRepository.existsByNikAndPersonaRoleIsLike(nik, personaRole);
    }

    @Transactional(readOnly = true)
    public boolean existsByCoCode(String coCode) {
        return cashUserPersonasRepository.existsByCoCode(coCode);
    }

    public List<CashUser> fetchAllPersonaDetailsForNik(String nik) throws DataNotFoundException {
        List<CashUserPersonasEntity> entityList = cashUserPersonasRepository.findPersonasForNik(nik);
        List<CashUser> result = entityList.stream().map(e -> {
            CashUser cashUser = new CashUser();
            cashUser.setCoCode(e.getCoCode());
            cashUser.setCoMail(e.getCoMail());
            cashUser.setCoName(e.getCoName());
            cashUser.setMmsCode(e.getMmsCode());
            cashUser.setMmsName(e.getMmsName());
            cashUser.setNik(e.getNik());
            cashUser.setPersonaRole(e.getPersonaRole());
            return cashUser;
        }).collect(Collectors.toList());
        if(result.size() == 0) {
            throw new DataNotFoundException("Unable to find data with specific requirement");
        }
        return result;
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW, isolation = Isolation.READ_UNCOMMITTED, rollbackFor = {DataNotFoundException.class, DataIntegrityException.class, Exception.class})
    public void updatePersonaMMSChild(String mmsCode, List<CashUserPersonasEntity> personas) throws DataNotFoundException, DataIntegrityException {
        CoverDanaMappingEntity cdm = coverDanaMappingRepository.findByMmsCode(mmsCode);
        if (cdm == null){
            throw new DataNotFoundException("MMS not found with mms code "+mmsCode);
        }else {
            if (cdm.getMmsType().equals(MmsType.DEFAULT)){
                throw new DataIntegrityException("MMS type not hub with mms code "+mmsCode);
            }
        }
        for (CashUserPersonasEntity persona : personas) {
            Optional<CashUserPersonasEntity> opt = cashUserPersonasRepository.findByNikAndCoCode(persona.getNik(), persona.getCoCode());
            if (opt.isPresent()){
                CashUserPersonasEntity personaEntity = opt.get();
                // FE send null mmsCode for remove mmschild
                if (persona.getMmsCode() == null || persona.getMmsCode() == ""){
                    personaEntity.setMmsCodeChild(null);
                    personaEntity.setMmsNameChild(null);
                }else {
                    personaEntity.setMmsCodeChild(cdm.getMmsCode());
                    personaEntity.setMmsNameChild(cdm.getMmsName());
                }
                personaEntity.setPersonaRole(persona.getPersonaRole());
                if (personaEntity.getPersonaRole().equalsIgnoreCase(CO)) {
                    personaEntity.setIsRoleSet("N");
                }
                personaEntity.setUpdatedDate(new Timestamp(new Date().getTime()));
                personaEntity = cashUserPersonasRepository.save(personaEntity);
                USER_PERSONAS_SERVICE_LOGGER.info("User persona sucess commit update with data {}",personaEntity.toString());
            }else {
                throw new DataNotFoundException("Persona with NIK "+persona.getNik()+" and CoCode "+persona.getCoCode()+" not found");
            }
        }
    }



    public void syncCashUserPersonaToProsperaLombok(String nik) throws NotFoundException {
        List<CashUserPersonasEntity> listCashUserPersonasProspera = userProsperaService.getFilteredProsperaUser(nik);


        if (listCashUserPersonasProspera.isEmpty()){
            cashUserPersonasRepository.deleteCashUserPersonasEntitiesByNik(nik);
            return;
        }

        List<String> personelCoCode = listCashUserPersonasProspera.stream().map(e->e.getCoCode().toUpperCase()).collect(Collectors.toList());
        List<CashUserPersonasEntity> listCashUser = cashUserPersonasRepository.findByNik(nik);

        listCashUser.forEach(e->{
            if (!personelCoCode.contains(e.getCoCode().toUpperCase())){
                cashUserPersonasRepository.deleteCashUserPersonas(nik,e.getCoCode());
            }
        });

        cashUserPersonasRepository.saveAll(listCashUserPersonasProspera.stream().map(userProspera->{


            CashUserPersonasEntity cashUserPersonasEntity =  listCashUser.stream()
                    .filter(cu-> cu.getCoCode().equalsIgnoreCase(userProspera.getCoCode())).findAny()
                    .orElse(userProspera);

            cashUserPersonasEntity.setCoName(userProspera.getCoName());
            cashUserPersonasEntity.setRoleId(userProspera.getRoleId());;

            if (!userProspera.getMmsCode().equalsIgnoreCase(cashUserPersonasEntity.getMmsCode())){
                cashUserPersonasEntity.setPersonaRole(userProspera.getPersonaRole());
                cashUserPersonasEntity.setMmsCode(userProspera.getMmsCode());
                cashUserPersonasEntity.setMmsName(userProspera.getMmsName());
            }

            if (!userProspera.getPersonaRole().equalsIgnoreCase(cashUserPersonasEntity.getPersonaRole()) &&
                    (!userProspera.getPersonaRole().equalsIgnoreCase(CO) || cashUserPersonasEntity.getPersonaRole().equalsIgnoreCase(BM))){
                cashUserPersonasEntity.setPersonaRole(userProspera.getPersonaRole());
            }

            return cashUserPersonasEntity;
        }).collect(Collectors.toList()));
    }

    public void syncProsperaPersonel(String nik){
        List<Integer> roleIds = cashManagementProsperaRoleMapRepository.findAll().stream().map(CashManagementProseperaRoleMapEntity::getProsperaRoleId).distinct().collect(Collectors.toList());
        List<PersonelProjection> personels = personnelRepository.findByNik(nik, roleIds);

        if (personels.isEmpty()){
            cashUserPersonasRepository.deleteCashUserPersonasEntitiesByNik(nik);
            return;
        }

        List<String> personelCoCode = personels.stream().map(e->e.getCoCode().toUpperCase()).collect(Collectors.toList());
        List<CashUserPersonasEntity> listCashUser = cashUserPersonasRepository.findByNik(nik);

        listCashUser.forEach(e->{
            if (!personelCoCode.contains(e.getCoCode().toUpperCase())){
                cashUserPersonasRepository.deleteCashUserPersonas(nik,e.getCoCode());
            }
        });

        cashUserPersonasRepository.saveAll(personels.stream().map(personel->{
            String roleName = findRoleIdName(personel.getRoleId());

            CashUserPersonasEntity cashUserPersonasEntity =  listCashUser.stream()
                    .filter(cu-> cu.getCoCode().equalsIgnoreCase(personel.getCoCode())).findAny()
                    .orElse(CashUserPersonasEntity.builder()
                            .coCode(personel.getCoCode())
                            .nik(personel.getNik())
                            .mmsCode(personel.getOfficeCode())
                            .mmsName(personel.getOfficeName())
                            .roleId(personel.getRoleId())
                            .personaRole(roleName)
                            .isRoleSet("N")
                            .updatedDate(new Timestamp(new Date().getTime()))
                            .build());
            cashUserPersonasEntity.setCoName(personel.getName());

            if (!personel.getOfficeCode().equalsIgnoreCase(cashUserPersonasEntity.getMmsCode())){
                cashUserPersonasEntity.setPersonaRole(roleName);
                cashUserPersonasEntity.setMmsCode(personel.getOfficeCode());
                cashUserPersonasEntity.setMmsName(personel.getOfficeName());
            }

            if (!roleName.equalsIgnoreCase(cashUserPersonasEntity.getPersonaRole()) &&
                    (!roleName.equalsIgnoreCase(CO) || cashUserPersonasEntity.getPersonaRole().equalsIgnoreCase(BM))){
                cashUserPersonasEntity.setPersonaRole(roleName);
            }

            return cashUserPersonasEntity;
        }).collect(Collectors.toList()));
    }

    private String findRoleIdName(int roleId){
        Optional<CashManagementProseperaRoleMapEntity> optional=cashManagementProsperaRoleMapRepository.findByProsperaRoleId(roleId);
        return optional.map(CashManagementProseperaRoleMapEntity::getCashManagementRoleName).orElse(null);
    }

    public String getCoNameByNik(String nik) {
        List<CashUserPersonasEntity> listPersona = cashUserPersonasRepository.findPersonasForNik(nik);
        if(listPersona.size() > 0) {
            return listPersona.get(0).getCoName();
        }
        return nik;
    }

    public String getCoNameByCoCode(String coCode) {
        Optional<CashUserPersonasEntity> optPersonas = cashUserPersonasRepository.findPersonaByCoCode(coCode);
        if(optPersonas.isPresent()) {
            return optPersonas.get().getCoName();
        }
        return "";
    }

    public Map<String, String> getMMSByCoCode(String coCode) {
        Map<String, String> mapResult = new HashMap<>();
        Optional<CashUserPersonasEntity> optional = cashUserPersonasRepository.findPersonaByCoCode(coCode);
        if(optional.isPresent()) {
            mapResult.put(optional.get().getMmsCode(), optional.get().getMmsName());
        }
        return mapResult;
    }

    public List<CashUser> getMMSRelatedDetailsByNik(String nik) throws DataNotFoundException {
        List<CashUserPersonasEntity> list = cashUserPersonasRepository.findPersonasForMmsCodesOfNik(nik);
        List<CashUser> result = list.stream().map(e -> {
            CashUser cashUser = new CashUser();
            cashUser.setCoName(e.getCoName());
            cashUser.setMmsCode(e.getMmsCode());
            cashUser.setMmsName(e.getMmsName());
            cashUser.setCoCode(e.getCoCode());
            cashUser.setCoMail(e.getCoMail());
            cashUser.setNik(e.getNik());
            cashUser.setPersonaRole(e.getPersonaRole());
            return cashUser;
        }).collect(Collectors.toList());
        if(result.size() == 0) {
            throw new DataNotFoundException("Unable to find data with specific requirement");
        }
        return result;
    }

    public Page<CashUserPersonasEntity> fetchAllUserPersonas(final String queryString, final Pageable pageable) {
        return cashUserPersonasRepository.findAll(createSpecifications(queryString), pageable);
    }

    public Page<CashUserPersonasEntity> fetchAllUserPersonasBm(final String queryString, final Pageable pageable, final String mmsCode) {
        return cashUserPersonasRepository.findAll(createSpecificationsForBm(queryString,mmsCode), pageable);
    }

    Specification<CashUserPersonasEntity> createSpecificationsForBm(final String queryString, final String mmsCode) {
        return where(mmsCodeAnd(mmsCode))
                .and(
                        createSpecifications(queryString)
                );
    }

    Specification<CashUserPersonasEntity> createSpecifications(final String queryString) {
        return where(nikLike(queryString))
                .or(coCodeLike(queryString))
                .or(coNameLike(queryString))
                .or(mmsCodeLike(queryString))
                .or(mmsNameLike(queryString));
    }

    static Specification<CashUserPersonasEntity> nikLike(final String nik) {
        return (cashUserPersona, cq, cb) -> cb.like(cashUserPersona.get("nik"), "%" + nik + "%");
    }

    static Specification<CashUserPersonasEntity> coCodeLike(final String coCode) {
        return (cashUserPersona, cq, cb) -> cb.like(cashUserPersona.get("coCode"), "%" + coCode + "%");
    }

    static Specification<CashUserPersonasEntity> coNameLike(final String coName) {
        return (cashUserPersona, cq, cb) -> cb.like(cashUserPersona.get("coName"), "%" + coName + "%");
    }

    static Specification<CashUserPersonasEntity> mmsCodeLike(final String mmsCode) {
        return (cashUserPersona, cq, cb) -> cb.like(cashUserPersona.get("mmsCode"), "%" + mmsCode + "%");
    }

    static Specification<CashUserPersonasEntity> mmsNameLike(final String mmsName) {
        return (cashUserPersona, cq, cb) -> cb.like(cashUserPersona.get("mmsName"), "%" + mmsName + "%");
    }

    static Specification<CashUserPersonasEntity> mmsCodeAnd(String mmsCode) {
        return (cashUserPersona, cq, cb) -> cb.equal(cashUserPersona.get("mmsCode"),mmsCode);
    }
    
    public List<CashUserPersonasEntity> getListCashUserPersonasByFlag(String value, String flag){
        // flag 1 search by nik, 2 search by co code, 3 search by mms
        List<CashUserPersonasEntity> listCashUser = new ArrayList<>();
        if (flag.equals("1")){
            listCashUser = cashUserPersonasRepository.findByNik(value);
            USER_PERSONAS_SERVICE_LOGGER.info("get all cash user personas by nik " + value);
        }else if(flag.equals("2")){
            listCashUser = cashUserPersonasRepository.findByCoCode(value);
            USER_PERSONAS_SERVICE_LOGGER.info("get all cash user personas by coCode " + value);
        }else if (flag.equals("3")){
            listCashUser = cashUserPersonasRepository.findByMmsCode(value);
            USER_PERSONAS_SERVICE_LOGGER.info("get all cash user personas by mmsCode " + value);
        }
        return listCashUser;
    }

    public void updateCashUserPersonas(CashUserPersonasUpdate cashUserPersonasUpdate) {
        CashUserPersonasEntity cashUserPersonasEntity = cashUserPersonasRepository.findPersonasByNikCoCodeMmsCodeAndRole(
                cashUserPersonasUpdate.getUserData().getNik(),
                cashUserPersonasUpdate.getUserData().getCoCode(),
                cashUserPersonasUpdate.getUserData().getMmsCode(),
                cashUserPersonasUpdate.getUserData().getPersonaRole()
                );
        if(cashUserPersonasEntity != null) {
            cashUserPersonasEntity.setCoCode(cashUserPersonasUpdate.getUpdatedData().getCoCode());
            cashUserPersonasEntity.setCoName(cashUserPersonasUpdate.getUpdatedData().getCoName());
            cashUserPersonasEntity.setCoMail(cashUserPersonasUpdate.getUpdatedData().getCoMail());
            cashUserPersonasEntity.setPersonaRole(cashUserPersonasUpdate.getUpdatedData().getPersonaRole());
            cashUserPersonasEntity.setIsRoleSet(cashUserPersonasUpdate.getUpdatedData().getIsRoleSet());
            cashUserPersonasEntity.setMmsCodeChild(cashUserPersonasUpdate.getUpdatedData().getMmsCodeChild());
            cashUserPersonasEntity.setMmsNameChild(cashUserPersonasUpdate.getUpdatedData().getMmsNameChild());
            cashUserPersonasEntity.setMobileId(cashUserPersonasUpdate.getUpdatedData().getMobileId());
            cashUserPersonasEntity.setMmsCode(cashUserPersonasUpdate.getUpdatedData().getMmsCode());
            cashUserPersonasEntity.setMmsName(cashUserPersonasUpdate.getUpdatedData().getMmsName());
            cashUserPersonasEntity.setUpdatedDate(new Timestamp(new Date().getTime()));

            cashUserPersonasRepository.save(cashUserPersonasEntity);
        } else {
            throw new DataNotFoundException("User personas not found!");
        }
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW, isolation = Isolation.READ_UNCOMMITTED, rollbackFor = {Exception.class})
    public boolean deleteCashUserPersonas(String nik, String coCode){
        Optional<CashUserPersonasEntity> cashUserPersonasOptional = cashUserPersonasRepository.findByNikAndCoCode(nik, coCode);
        if (!cashUserPersonasOptional.isPresent()){
            throw new DataNotFoundException("Persona with NIK "+nik+" and CoCode "+coCode+" not found");
        }
        USER_PERSONAS_SERVICE_LOGGER.info("Success Delete Cash user personas with nik "+nik+"and coCode"+coCode);
        cashUserPersonasRepository.deleteCashUserPersonas(nik,coCode);
        return true;
    }

    public boolean saveCashUserPersonas(CashUserPersonasEntity cashUserPersonasEntity) throws DatabaseTransactionException{
        Optional<CashUserPersonasEntity> cashUserPersonasOptional = cashUserPersonasRepository.findByNikAndCoCode(cashUserPersonasEntity.getNik(), cashUserPersonasEntity.getCoCode());
        if (cashUserPersonasOptional.isPresent()){
            throw new DatabaseTransactionException("Persona with NIK "+cashUserPersonasEntity.getNik()+" and CoCode "+cashUserPersonasEntity.getCoCode()+" already exsist");
        }
//        CoverDanaMappingEntity coverDanaMappingEntity = coverDanaMappingRepository.findByMmsCode(cashUserPersonasEntity.getMmsCode());
//        if (coverDanaMappingEntity == null){
//            throw new DataNotFoundException("MMS not found with mms code "+cashUserPersonasEntity.getMmsCode());
//        }

        USER_PERSONAS_SERVICE_LOGGER.info("Saving Cash user personas with nik "+cashUserPersonasEntity.getNik()+" and coCode "+cashUserPersonasEntity.getCoCode());
        cashUserPersonasEntity.setUpdatedDate(new Date());
        cashUserPersonasRepository.save(cashUserPersonasEntity);
        return true;
    }

    public List<CashUserAssignModel> getCashUserAssignAll(){
        List<Object[]> list = cashUserPersonasRepository.findAssignedDateCashUserPersonasAll();

        List<CashUserAssignModel> cashAssignList = new ArrayList<>();
        for (Object[] modelCashAssignObject : list){
            CashUserAssignModel cashUserAssignModel = new CashUserAssignModel();
            cashUserAssignModel.setNik((String)modelCashAssignObject[0]);
            cashUserAssignModel.setMmsCode((String) modelCashAssignObject[1]);
            cashUserAssignModel.setMmsName((String)modelCashAssignObject[2]);
            cashUserAssignModel.setCoCode((String) modelCashAssignObject[3]);
            cashUserAssignModel.setCoName((String) modelCashAssignObject[4]);
            cashUserAssignModel.setPersonaRole((String) modelCashAssignObject[5]);
            cashUserAssignModel.setAssignedDate((Date) modelCashAssignObject[6]);
            cashAssignList.add(cashUserAssignModel);
        }
        return cashAssignList;
    }

    public void assignAlerts(List<String> mmsCodes, KasHomeModel kasHomeModel, UserDetailEntity userDetail, String userId){
        List<CashUserPersonasEntity> personas = cashUserPersonasRepository.findByNik(userDetail.getUserIdentifier());
        String mmsCode;
        Map<String, String> mmsTree = new HashMap<>();

        if (!personas.isEmpty()) {
            CashUserPersonasEntity persona = personas.get(0);
            if (persona.getMmsCodeChild() != null) {
                mmsCodes.add(persona.getMmsCodeChild());
                mmsTree.put(persona.getMmsCode(), persona.getMmsCodeChild());
                log.info("User login at MMS Hub: {}", persona.getMmsCodeChild());
            }
        }

        List<CoverDanaMappingEntity> coverDanaMappings = coverDanaMappingRepositoryV2.findByMmsCode(mmsCodes);
        Date today = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

        for (String key : kasHomeModel.getMappedMmsDetails().keySet()) {

            mmsCode = key;
            String finalMmsCode = mmsTree.get(key) != null && !mmsTree.get(key).isEmpty() ? mmsTree.get(key) : mmsCode;

            CoverDanaMappingEntity cdm = coverDanaMappings.stream()
                    .filter(c -> c.getMmsCode().equalsIgnoreCase(finalMmsCode))
                    .findFirst().orElse(null);

            kasHomeModel.getMappedMmsDetails().get(key).setNoKomOutdatedAlert(false);
            kasHomeModel.getMappedMmsDetails().get(key).setNoKomOutdatedAlertMessage("");

            kasHomeModel.getMappedMmsDetails().get(key).setNoKomPicLembesChangedAlert(false);
            kasHomeModel.getMappedMmsDetails().get(key).setNoKomPicLembesChangedAlertMessage("");

            FPBAgingAlertProjection fpbAgingAlertMessage = fpbService.getFpbAgingAlertMessage(finalMmsCode, userId);

            if(fpbAgingAlertMessage != null) {
                kasHomeModel.getMappedMmsDetails().get(key).setFpbAgingAlertProjection(fpbAgingAlertMessage);
            }

            if(cdm == null){
                continue;
            }

            if (cdm.getLatestLembesNoKomUpdated() != null) {
                long days = DateHandler.dateDiffInDays(today, cdm.getLatestLembesNoKomUpdated());

                // if no kom not yet changed after 6 months
                if (days >= (6 * 30)) {
                    kasHomeModel.getMappedMmsDetails().get(key).setNoKomOutdatedAlert(true);
                    kasHomeModel.getMappedMmsDetails().get(key).setNoKomOutdatedAlertMessage("Sudah lebih dari 6 bulan belum dilakukan pergantian nomor kombinasi lemari besi");
                }

                // if latest update pic lembes > latest update no kom
                // then no kom should be changed
                // i.e latest update pic lembes: 2021-09-01 and latest update no kom = 2021-04-01
                if(cdm.getLatestPicLembesChangeUpdated() != null &&
                    cdm.getLatestPicLembesChangeUpdated().getTime() > cdm.getLatestLembesNoKomUpdated().getTime()){

                    // MF-1267 - when PIC lembes updated in same day with latest no kom updated
                    // it will not warn the user
                    String latestPicLembesUpdated = sdf.format(cdm.getLatestPicLembesChangeUpdated());
                    String latestNoKomUpdated = sdf.format(cdm.getLatestLembesNoKomUpdated());

                    if (!latestNoKomUpdated.equals(latestPicLembesUpdated) && !similarPreviousPIC(PersonaRole.LB, finalMmsCode)) {
                        kasHomeModel.getMappedMmsDetails().get(key).setNoKomPicLembesChangedAlert(true);
                        kasHomeModel.getMappedMmsDetails().get(key).setNoKomPicLembesChangedAlertMessage("Jangan lupa melakukan penggantian nomor kunci kombinasi setiap pergantian PIC Lemari Besi");
                    }
                }
            }
        }
    }

    //To check if previous PIC is similar to current
    private boolean similarPreviousPIC(PersonaRole role, String mmsCode) {
        return cashUserPersonasLogRepository.findTop2ByRoleAndMmsCode(role.getPersonaRole(), mmsCode)
                .stream()
                .distinct().count() == 1;
    }

    public CashUserPersonasEntity findTop1PersonaByNik(String userId) {
        return cashUserPersonasRepository.findTop1PersonaByNik(userId);
    }
}
