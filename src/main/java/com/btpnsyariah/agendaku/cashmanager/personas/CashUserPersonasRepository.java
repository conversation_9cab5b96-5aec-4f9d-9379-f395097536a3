package com.btpnsyariah.agendaku.cashmanager.personas;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import javax.transaction.Transactional;
import java.util.List;
import java.util.Optional;

@Repository
public interface CashUserPersonasRepository extends JpaRepository<CashUserPersonasEntity, Integer>, JpaSpecificationExecutor<CashUserPersonasEntity> {

    @Query("select c from CashUserPersonasEntity c " +
            "where c.mmsCode = :mmsCode " +
            "and c.personaRole IN :roles ")
    List<CashUserPersonasEntity> findByMMSCode(String mmsCode, List<String> roles);

    @Query(nativeQuery = true, value = "SELECT * FROM dbo.CashUserPersonas tb WHERE tb.MMSCode = :mmsCode AND tb.PersonaRole NOT IN ('BM') ORDER BY tb.COCode")
    List<CashUserPersonasEntity> findAllByMmsCodeOrderByCoCodeAsc(@Param("mmsCode") final String mmsCode);

    @Query(nativeQuery = true, value = "SELECT * FROM dbo.CashUserPersonas tb WHERE ((tb.MMSCode = :mmsCode AND (tb.MMSCodeChild is null OR tb.MMSCodeChild='')) OR tb.MMSCodeChild = :mmsCode) AND tb.PersonaRole NOT IN ('BM') AND MobileId is not null ORDER BY tb.COCode")
    List<CashUserPersonasEntity> findAllByMmsCodeAndMobileIdNotNullOrderByCoCodeAsc(@Param("mmsCode") final String mmsCode);

    @Query(nativeQuery = true, value = "SELECT * FROM dbo.CashUserPersonas tb WHERE tb.MMSCode = :mmsCode ORDER BY tb.COCode")
    List<CashUserPersonasEntity> findAllByMmsCodeWithBmOrderByCoCodeAsc(@Param("mmsCode") final String mmsCode);

    List<CashUserPersonasEntity> findAllByMmsCodeAndPersonaRole(String mmsCode, String personaRole);

    @Query(nativeQuery = true, value = "SELECT * FROM dbo.CashUserPersonas tb WHERE tb.MMSCode IN (select MMSCode from CashUserPersonas WHERE NIK = :nik) AND tb.PersonaRole NOT IN ('BM') ORDER BY tb.COCode")
    List<CashUserPersonasEntity> findAllByMmsCodeForGivenNikOrderByCoCodeAsc(@Param("nik") final String nik);

    @Query(nativeQuery = true, value = "SELECT * FROM dbo.CashUserPersonas tb WHERE tb.MMSCode IN :mmsCodes AND tb.PersonaRole NOT IN ('BM') ORDER BY tb.COCode")
    List<CashUserPersonasEntity> findAllByMmsCodesOrderByCoCodeAsc(@Param("mmsCodes") List<String> mmsCodes);

    @Query(nativeQuery = true, value = "SELECT * FROM dbo.CashUserPersonas tb WHERE tb.MMSCode IN (select MMSCode from CashUserPersonas WHERE NIK = :nik) AND tb.IsRoleSet = 'Y' ORDER BY tb.COCode")
    List<CashUserPersonasEntity> findPersonasForMmsCodesOfNik(@Param("nik") final String nik);

    @Query(nativeQuery = true, value = "SELECT * FROM dbo.CashUserPersonas tb WHERE tb.MMSCode IN :mmsCodes AND tb.IsRoleSet = 'Y' ORDER BY tb.COCode")
    List<CashUserPersonasEntity> findPersonasByMmsCodeList(@Param("mmsCodes") final List<String> mmsCodes);

    @Query(nativeQuery = true, value = "SELECT TOP 1 * FROM dbo.CashUserPersonas tb WHERE tb.CoCode = :coCode")
    Optional<CashUserPersonasEntity> findPersonaByCoCode(@Param("coCode") String coCode);

    @Query(nativeQuery = true, value = "SELECT * FROM dbo.CashUserPersonas WHERE NIK = :nik")
    List<CashUserPersonasEntity> findPersonasForNik(@Param("nik") final String nik);

    @Query(nativeQuery = true, value = "SELECT * FROM dbo.CashUserPersonas WHERE NIK = :nik AND FCMToken IS NOT NULL ORDER BY UpdatedDate desc")
    List<CashUserPersonasEntity> findPersonasByNikAndFcmTokenNotNull(@Param("nik") final String nik);

    @Query(nativeQuery = true, value = "SELECT * FROM dbo.CashUserPersonas WHERE MMSCode = :mmsCode AND PersonaRole = :personaRole AND FCMToken IS NOT NULL")
    List<CashUserPersonasEntity> findPersonasByMmsAndRoleAndFcmNotNull(@Param("mmsCode") String mmsCode, @Param("personaRole") String personaRole);

    @Query(nativeQuery = true, value = "SELECT TOP 1 * FROM dbo.CashUserPersonas WHERE MMSCode = :mmsCode AND PersonaRole = :personaRole AND FCMToken IS NOT NULL ORDER BY UpdatedDate DESC")
    CashUserPersonasEntity findTop1PersonaByMmsAndRoleAndFcmNotNull(@Param("mmsCode") String mmsCode, @Param("personaRole") String personaRole);

    @Query(nativeQuery = true, value = "SELECT TOP 1 * FROM dbo.CashUserPersonas WHERE NIK = :nik AND FCMToken IS NOT NULL ORDER BY UpdatedDate desc")
    CashUserPersonasEntity findTop1PersonaByNikAndFcmTokenNotNull(@Param("nik") final String nik);

    @Query(nativeQuery = true, value = "SELECT TOP 1 * FROM dbo.CashUserPersonas WHERE NIK = :nik ORDER BY UpdatedDate desc")
    CashUserPersonasEntity findTop1PersonaByNik(@Param("nik") final String nik);

    @Query(nativeQuery = true, value = "SELECT TOP 1 * FROM dbo.CashUserPersonas " +
            "WHERE MMSCode = :mmsCode AND PersonaRole = :personaRole AND MMSCodeChild IS NULL AND MMSNameChild IS NULL " +
            "OR MMSCode = :mmsCode AND PersonaRole = :personaRole AND MMSCodeChild = '' AND MMSNameChild = ''")
    CashUserPersonasEntity findPersonasByMmsAndRole(@Param("mmsCode") String mmsCode, @Param("personaRole") String personaRole);

    @Query(nativeQuery = true, value = "SELECT TOP 1 * FROM dbo.CashUserPersonas WHERE MMSCodeChild = :mmsCode AND PersonaRole = :personaRole")
    CashUserPersonasEntity findPersonasByMmsAndRoleHub(@Param("mmsCode") String mmsCode, @Param("personaRole") String personaRole);

    @Query(nativeQuery = true, value = "SELECT * FROM dbo.CashUserPersonas WHERE MMSCode = :mmsCode AND (PersonaRole = 'KFO' OR PersonaRole = 'KFO_AUTHORIZER') ")
    List<CashUserPersonasEntity> findPersonasByKfoAndRole(@Param("mmsCode") String mmsCode);

    @Modifying
    @Transactional
    @Query(nativeQuery = true, value ="update dbo.CashUserPersonas set PersonaRole = :role WHERE COCode = :coCode AND NIK = :nik AND IsRoleSet = 'N'")
    int updateRoleForUser(@Param("role") String role, @Param("coCode") String coCode, @Param("nik") String nik);

    @Modifying
    @Transactional
    @Query(nativeQuery = true, value ="UPDATE dbo.CashUserPersonas SET AccessToken = :accessToken WHERE COCode = :coCode AND NIK = :nik")
    int updateAccessTokenForUser(@Param("accessToken") String accessToken, @Param("coCode") String coCode, @Param("nik") String nik);

    @Query(nativeQuery = true, value = "SELECT tb.MMSCode, tb.MMSName, tb.MprosEstimation, tb.UseCashManagement, \n" +
            "ISNULL(bt.BalanceAmount,0) lemariBesiBalance, ISNULL(btkw.BalanceAmount,0) kasWismaBalance,\n" +
            "CASE WHEN tb.LBLimit = 0 OR tb.LBLimit IS NULL THEN CAST(0 AS BIT)\n" +
            "\tELSE \n" +
            "\t\tCASE WHEN bt.BalanceAmount > tb.LBLimit THEN CAST(1 AS BIT)\n" +
            "\t\tELSE CAST(0 AS BIT)\n" +
            "\t\tEND\n" +
            "\tEND isWarningLimitLB,\n" +
            "\t tb.MMSType, \n" +
            "\t ad.MmsCodeChild, \n" +
            "\t tb.MMSCode mmsParent,   \n" +
            "\t CASE WHEN cdm.MmsCode is not null AND cdm.MMSType = 'HUB' THEN cdm.UseJournalPosting\n" +
            "\t\tELSE tb.UseJournalPosting\n" +
            "\t\tEND,\n" +
            "\t tb.UseCashOpname, \n" +
            "\t ad.MmsNameChild, \n" +
            "\t tb.IsPiloting \n" +
            "FROM CoverDanaMapping tb \n" +
            "INNER JOIN CashUserPersonas ad ON tb.MMSCode = ad.MMSCode \n" +
            "LEFT JOIN CoverDanaMapping cdm ON cdm.MMSCode = ad.MmsCodeChild \n" +
            "INNER JOIN BalanceTransaction bt on bt.MMSCode = tb.MMSCode AND bt.BalanceTransactionType = 'LB_VAULT'\n" +
            "INNER JOIN BalanceTransaction btkw on btkw.MMSCode = tb.MMSCode AND btkw.BalanceTransactionType = 'KW_CASHBOX'\n" +
            "WHERE ad.NIK = :nik ORDER BY tb.MMSCode")
    List<Object[]> findMmsRelatedDetailsByNik(@Param("nik") final String nik);

    @Query(nativeQuery = true, value = "SELECT tb.MMSCode, tb.MMSName, tb.MprosEstimation, tb.UseCashManagement,     \n" +
            "             ISNULL(bt.BalanceAmount,0) lemariBesiBalance, ISNULL(btkw.BalanceAmount,0) kasWismaBalance,    \n" +
            "             CASE WHEN tb.LBLimit = 0 OR tb.LBLimit IS NULL THEN CAST(0 AS BIT)    \n" +
            "              ELSE     \n" +
            "               CASE WHEN bt.BalanceAmount > tb.LBLimit THEN CAST(1 AS BIT)    \n" +
            "               ELSE CAST(0 AS BIT)    \n" +
            "               END    \n" +
            "              END isWarningLimitLB,    \n" +
            "               tb.MMSType,     \n" +
            "               ad.MmsCodeChild,     \n" +
            "               cdm.MMSCode mmsParent,   \n" +
            "\t             tb.UseJournalPosting, \n" +
            "\t             tb.UseCashOpname, \n" +
            "\t             ad.MmsNameChild, \n" +
            "\t             tb.IsPiloting \n" +
            "             FROM CoverDanaMapping cdm     \n" +
            "             INNER JOIN CashUserPersonas ad ON cdm.MMSCode = ad.MMSCode \n" +
            "\t\t\t INNER JOIN CoverDanaMapping tb ON cdm.MMSCode = tb.MMSCodeParent OR cdm.MMSCode =tb.MMSCode\n" +
            "             left JOIN BalanceTransaction bt on bt.MMSCode = tb.MMSCode AND bt.BalanceTransactionType = 'LB_VAULT'    \n" +
            "             left JOIN BalanceTransaction btkw on btkw.MMSCode = tb.MMSCode AND btkw.BalanceTransactionType = 'KW_CASHBOX'  " +
            "WHERE ad.NIK = :nik ORDER BY tb.MMSCode")
    List<Object[]> findMmsRelatedDetailsByNikForBM(@Param("nik") final String nik);

    @Query(nativeQuery = true, value = "SELECT tb.MMSCode, tb.MMSName, tb.MprosEstimation, tb.UseCashManagement,     \n" +
            "             ISNULL(bt.BalanceAmount,0) lemariBesiBalance, ISNULL(btkw.BalanceAmount,0) kasWismaBalance,    \n" +
            "             CASE WHEN tb.LBLimit = 0 OR tb.LBLimit IS NULL THEN CAST(0 AS BIT)    \n" +
            "              ELSE     \n" +
            "               CASE WHEN bt.BalanceAmount > tb.LBLimit THEN CAST(1 AS BIT)    \n" +
            "               ELSE CAST(0 AS BIT)    \n" +
            "               END    \n" +
            "              END isWarningLimitLB,    \n" +
            "               tb.MMSType,     \n" +
            "               '' MmsCodeChild,     \n" +
            "               cdm.MMSCode mmsParent,   \n" +
            "\t             tb.UseJournalPosting, \n" +
            "\t             tb.UseCashOpname, \n" +
            "\t             '' MmsNameChild, \n" +
            "\t             tb.IsPiloting \n" +
            "             FROM CoverDanaMapping cdm     \n" +
//            "             INNER JOIN CashUserPersonas ad ON cdm.MMSCode = ad.MMSCode \n" +
            "\t\t\t INNER JOIN CoverDanaMapping tb ON cdm.MMSCode = tb.MMSCodeParent OR cdm.MMSCode =tb.MMSCode\n" +
            "             left JOIN BalanceTransaction bt on bt.MMSCode = tb.MMSCode AND bt.BalanceTransactionType = 'LB_VAULT'    \n" +
            "             left JOIN BalanceTransaction btkw on btkw.MMSCode = tb.MMSCode AND btkw.BalanceTransactionType = 'KW_CASHBOX'  " +
            "WHERE cdm.MMSCode in :mmsCodes ORDER BY tb.MMSCode")
    List<Object[]> findMmsRelatedDetailsByNikForBMHO(@Param("mmsCodes") final List<String> mmsCodes);

    boolean existsByNikAndPersonaRoleIsLike(String nik, String personaRole);

    boolean existsByNikAndMmsCode(String nik, String mmsCode);



    List<CashUserPersonasEntity> findByMmsCodeAndPersonaRoleIn(String mmsCode, List<String> personaRole);

    boolean existsByCoCode(String coCode);

    List<CashUserPersonasEntity> findByNik(String nik);

    @Query(nativeQuery = true, value = "SELECT * FROM CashUserPersonas WHERE NIK = :nik AND PersonaRole = 'BM'")
    List<CashUserPersonasEntity> findByNikAndRole(String nik);

    List<CashUserPersonasEntity> findByCoCode(String coCode);

    List<CashUserPersonasEntity> findByMmsCode(String mmsCode);

    List<CashUserPersonasEntity> findByMobileId(String mobileId);

    Optional<CashUserPersonasEntity> findTop1ByMmsCodeAndPersonaRole(String mmsCode, String personaRole);

    Optional<CashUserPersonasEntity> findByNikAndCoCode(String nik, String coCode);

    @Transactional
    void deleteCashUserPersonasEntitiesByNik(String nik);

    @Modifying
    @Transactional
    @Query(nativeQuery = true, value = "DELETE dbo.CashUserPersonas WHERE COCode = :coCode AND NIK = :nik")
    int deleteCashUserPersonas(String nik, String coCode);


    @Query(nativeQuery = true, value = "SELECT cu.Nik, cu.MMSCode, cu.MMSName, cu.CoCode, cu.CoName, cu.PersonaRole, \n" +
            "CASE WHEN cu.PersonaRole NOT IN ('BM','CO') \n" +
            "THEN (SELECT TOP 1 cul.CreatedDate FROM dbo.CashUserPersonasLog cul WITH (NOLOCK) WHERE cul.NewUser = cu.Nik ORDER BY cul.CreatedDate desc)\n" +
            "END AssignedDate \n" +
            "FROM dbo.CashUserPersonas cu WITH (NOLOCK) " +
            "where PersonaRole in ('KAS_LEMARI_BESI','KAS_CADANGAN','KAS_CADANGAN_TAMBAHAN') \n" +
            "ORDER BY cu.MMSCode")
    List<Object[]> findAssignedDateCashUserPersonasAll();

    @Query("select cup from CashUserPersonasEntity cup where cup.nik in :niks")
    List<CashUserPersonasEntity> findByNiks(@Param("niks") List<String> niks);

    @Query(nativeQuery = true, value = "SELECT TOP 1 * FROM CashUserPersonas cup WHERE cup.Nik = :nik AND cup.CoCode = :coCode " +
            "AND cup.MMSCode = :mmsCode AND cup.PersonaRole = :personaRole")
    CashUserPersonasEntity findPersonasByNikCoCodeMmsCodeAndRole(
            @Param("nik") String nik,
            @Param("coCode") String coCode,
            @Param("mmsCode") String mmsCode,
            @Param("personaRole") String personaRole
    );

    @Query(nativeQuery = true, value = "SELECT TOP 1 * FROM CashUserPersonas cup WHERE cup.Nik = :nik AND cup.PersonaRole = 'KFO_AUTHORIZER'")
    CashUserPersonasEntity findKfoAuthByNik(@Param("nik") String nik);

    @Query(nativeQuery = true, value = "SELECT TOP 1 * FROM CashUserPersonas cup WHERE cup.Nik = :nik AND cup.CoCode = :coCode ORDER BY cup.UpdatedDate")
    CashUserPersonasEntity findPersonasByNikAndCoCode(@Param("nik") String nik, @Param("coCode") String coCode);

    @Query(nativeQuery = true, value = "SELECT * FROM CashUserPersonas cup WHERE cup.MMSCode = CONCAT('K', (SELECT TOP 1 KfoCode FROM CoverDanaMapping WHERE MMSCode = :mmsCode ORDER BY UpdatedDate DESC))")
    List<CashUserPersonasEntity> findByKfoPersonasByMmsCode(String mmsCode);


    @Query(nativeQuery = true, value = "Select top 1 isnull(cup.CoName,fpbr.CreatedBy) from FPBRequest fpbr " +
            "left join CashUserPersonas cup on fpbr.CreatedBy = cup.nik " +
            "where fpbr.LbDailyTransactionId = :lbId OR fpbr.AdditionalSettlementId = :lbId OR fpbr.RefundId = :lbId " +
            "order by cup.UpdatedDate DESC")
    Optional<String> findCoNameForFpbRequestByLbId(@Param("lbId") long lbId);

    @Query(nativeQuery = true, value = "Select top 1 isnull(cup.CoName,fpbrev.CreatedBy) from FPBReversal fpbrev " +
            "left join CashUserPersonas cup on fpbrev.CreatedBy = cup.nik " +
            "where fpbrev.ReverseSettlementId = :lbId OR fpbrev.AdditionalSettlementId = :lbId " +
            "order by cup.UpdatedDate DESC")
    Optional<String> findCoNameForFpbReversalByLbId(@Param("lbId") long lbId);
}
