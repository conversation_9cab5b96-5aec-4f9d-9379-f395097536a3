package com.btpnsyariah.agendaku.cashmanager.personas;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * DTO for CashUserPersonas entity
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CashUserPersonasDto {
    private String mmsCode;
    private String mmsName;
    @NotNull
    private String nik;
    @NotBlank
    private String coCode;
    private String coName;
    private String coMail;
    private String personaRole;
    private String isRoleSet;
    private Date updatedDate;
    private String mmsCodeChild;
    private String mmsNameChild;
    private String mobileId;
    private String fcmToken;
    private String accessToken;
    private Boolean pilotingToggle;
    private int roleId;
}
