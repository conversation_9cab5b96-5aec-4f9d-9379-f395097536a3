package com.btpnsyariah.agendaku.cashmanager.personas;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CashUserPersonasDto{
    private String mmsCode;
    private String mmsName;
    private @NotNull String nik;
    private @NotBlank String coCode;
    private String coName;
    private String coMail;
    private String personaRole;
    private String isRoleSet;
    private Date updatedDate;
    private String mmsCodeChild;
    private String mmsNameChild;
    private Boolean pilotingToggle;
    int roleId;
}
