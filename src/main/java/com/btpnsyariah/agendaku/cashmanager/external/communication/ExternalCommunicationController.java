package com.btpnsyariah.agendaku.cashmanager.external.communication;

import com.btpnsyariah.agendaku.cashmanager.exception.BusinessException;
import com.btpnsyariah.agendaku.cashmanager.model.response.BaseResponse;
import com.btpnsyariah.agendaku.cashmanager.model.vo.ExternalServiceDto;
import com.btpnsyariah.agendaku.cashmanager.model.vo.MMSDetailDto;
import com.btpnsyariah.agendaku.cashmanager.personas.*;
import com.btpnsyariah.agendaku.cashmanager.service.MMSService;
import com.btpnsyariah.agendaku.cashmanager.service.MMSServiceImpl;
import com.btpnsyariah.agendaku.cashmanager.sprint33.transaction.dailytransaction.codailytransaction.CoDailyTransactionHistoryGrouped;
import com.btpnsyariah.agendaku.cashmanager.sprint33.transaction.dailytransaction.codailytransaction.CoDailyTransactionServiceV5;
import com.btpnsyariah.agendaku.cashmanager.uservalidator.InvalidUserException;
import com.btpnsyariah.agendaku.cashmanager.uservalidator.User;
import com.btpnsyariah.agendaku.cashmanager.uservalidator.UserAuthResponse;
import com.btpnsyariah.agendaku.cashmanager.uservalidator.UserValidatorService;
import com.btpnsyariah.agendaku.cashmanager.util.DataNotFoundException;
import com.btpnsyariah.agendaku.cashmanager.util.Utility;
import com.btpnsyariah.agendaku.stdbydb.transaction.dailytransaction.model.DailyTransactionGroupedDTO;
import com.btpnsyariah.agendaku.stdbydb.transaction.dailytransaction.service.DailyTransactionService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.HttpClientErrorException;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.Size;
import java.io.UnsupportedEncodingException;
import java.util.List;

@RestController
@Validated
@RequestMapping("/external/communication")
public class ExternalCommunicationController {

    private static final Logger EXTERNAL_CONTROLLER_LOGGER = LoggerFactory.getLogger(ExternalCommunicationController.class);
    private static final Logger CASH_HOME_LOGGER = LoggerFactory.getLogger(CashUserPersonasController.class);


    private static final String REGEX_SPLIT = ":";
    private static final String KEY_FACTOR = "keyFactor";

    @Autowired
    UserValidatorService userValidatorService;

    @Autowired
    CashUserPersonasService cashUserPersonasService;

    @Autowired
    CoDailyTransactionServiceV5 coDailyTransactionService;

    @Autowired
    DailyTransactionService dailyTransactionService;

    @GetMapping("/user/details")
    public ResponseEntity getCashPersonaDetailsByUser(HttpServletRequest httpServletRequest) {
        User user = new User();
        try {
            user = getUserFromKeyFactor(httpServletRequest.getHeader(KEY_FACTOR));
            EXTERNAL_CONTROLLER_LOGGER.info("Getting key External API success with user {}",user.toString());
        }catch (UnsupportedEncodingException e) {
            EXTERNAL_CONTROLLER_LOGGER.error("External API user detail UnsupportedEncodingException error with message {}",e.getMessage());
            return new ResponseEntity("Unsupported Encoding Exception", HttpStatus.BAD_REQUEST);
        }

        try {
            user = getUserFromKeyFactor(httpServletRequest.getHeader(KEY_FACTOR));
            UserAuthResponse userResponse = userValidatorService.getUserAuth(user);
            List<CashUser> result = cashUserPersonasService.fetchAllPersonaDetailsForNik(user.getUsername());
            userResponse.setUserDetails(result);
            return new ResponseEntity(userResponse, HttpStatus.OK);
        } catch (HttpClientErrorException e) {
            EXTERNAL_CONTROLLER_LOGGER.error("External API user detail HttpClientErrorException error with message {} and nik {}",e.getMessage(),user.toString());
            return new ResponseEntity("Unable to access agendaku", HttpStatus.UNAUTHORIZED);
        } catch (DataNotFoundException e) {
            EXTERNAL_CONTROLLER_LOGGER.error("External API user detail DataNotFoundException error with message {} and nik {}",e.getMessage(),user.toString());
            return new ResponseEntity("Unable to fetch cash user persona details", HttpStatus.NOT_FOUND);
        }  catch (Exception e){
            EXTERNAL_CONTROLLER_LOGGER.error("External API user detail Exception error with message {} and nik {}",e.getMessage(),user.toString());
            return new ResponseEntity("Unhandle Exception", HttpStatus.BAD_REQUEST);
        }
    }

    @GetMapping("/mms/details")
    public ResponseEntity getMMSRelatedDetailsByUser(HttpServletRequest httpServletRequest) {
        User user = new User();
        try {
            user = getUserFromKeyFactor(httpServletRequest.getHeader(KEY_FACTOR));
            EXTERNAL_CONTROLLER_LOGGER.info("Getting key External API success with user {}",user.toString());
        }catch (UnsupportedEncodingException e) {
            EXTERNAL_CONTROLLER_LOGGER.error("External API mms detail UnsupportedEncodingException error with message {}",e.getMessage());
            return new ResponseEntity("Unsupported Encoding Exception", HttpStatus.BAD_REQUEST);
        }
        try {
            userValidatorService.getUserAuth(user);
            List<CashUser> result = cashUserPersonasService.getMMSRelatedDetailsByNik(user.getUsername());
            return new ResponseEntity(result, HttpStatus.OK);
        } catch (InvalidUserException e) {
            EXTERNAL_CONTROLLER_LOGGER.error("External API mms detail HttpClientErrorException error with message {} and nik {}",e.getMessage(),user.toString());
            if(e.getMessage().contains("401")) {
                return new ResponseEntity("Password yang diinput salah atau expired", HttpStatus.UNAUTHORIZED);
            } else if(e.getMessage().contains("403") || e.getMessage().contains("400")) {
                return new ResponseEntity("Akun atau password anda expired, mohon lakukan penggantian password di WEB corpmail.btpnsyariah.com (OWA) melalui link dibawah ini<br />https://corpmail.btpnsyariah.com/reset", HttpStatus.FORBIDDEN);
            } else if(e.getMessage().contains("423")) {
                return new ResponseEntity("User id Anda terkunci, silahkan coba sesudah 15 menit", HttpStatus.LOCKED);
            } else {
                return new ResponseEntity("Maaf telah terjadi kesalahan, silahkan coba beberapa saat lagi", HttpStatus.INTERNAL_SERVER_ERROR);
            }
        } catch (DataNotFoundException e) {
            EXTERNAL_CONTROLLER_LOGGER.error("External API mms detail DataNotFoundException error with message {} and nik {}",e.getMessage(),user.toString());
            return new ResponseEntity("Unable to fetch mms details", HttpStatus.NOT_FOUND);
        } catch (Exception e){
            EXTERNAL_CONTROLLER_LOGGER.error("External API mms detail Exception error with message {} and nik {}",e.getMessage(),user.toString());
            return new ResponseEntity("Unhandled Exception", HttpStatus.BAD_REQUEST);
        }
    }

    @Autowired
    private MMSService mmsService;

    @GetMapping("/mms/details/{mmsCode}")
    public ResponseEntity<ExternalServiceDto> getMMSCodeDetail(@PathVariable String mmsCode, HttpServletRequest httpServletRequest) {
        MMSDetailDto mmsDetailDto;
        try {
            mmsDetailDto = mmsService.getMmsDetail(mmsCode);
            ExternalServiceDto externalServiceDto = ExternalServiceDto.builder()
                    .code("00")
                    .data(mmsDetailDto)
                    .build();
            return new ResponseEntity<>(externalServiceDto, HttpStatus.OK);
        } catch (BusinessException e) {
            ExternalServiceDto externalServiceDto = ExternalServiceDto.builder()
                    .code(e.getCode())
                    .message(e.getMessage())
                    .data(null)
                    .build();
            return new ResponseEntity<>(externalServiceDto, HttpStatus.BAD_REQUEST);
        }
    }

    @GetMapping("/co/daily-transaction/hist")
    public ResponseEntity getCoDailyTransactionHistory(
            @RequestHeader("mmsCode")
                @Size(min = 3, message = "MMS Code is too short!")
                @Size(max = 10, message = "MMS Code is too long") String mmsCode,
            @RequestParam(value = "date", required = false) String date
    ) {
        try {
            List<CoDailyTransactionHistoryGrouped> coDailyTransactionHistoryGrouped = coDailyTransactionService.fetchAllCoDailyTransaction(mmsCode, date);
            if(!coDailyTransactionHistoryGrouped.isEmpty()) {
                EXTERNAL_CONTROLLER_LOGGER.error("External API for Summary DMK success with MMS Code " + mmsCode);
                return new ResponseEntity(
                        new BaseResponse(true,
                                "Success fetching CO daily transaction with MMS Code : " + mmsCode,
                                coDailyTransactionHistoryGrouped,
                                "200"), HttpStatus.OK);
            } else {
                return new ResponseEntity<>(HttpStatus.NO_CONTENT);
            }
        } catch (Exception e) {
            EXTERNAL_CONTROLLER_LOGGER.error("External API for Summary DMK error : " + e);
            return new ResponseEntity(
                    new BaseResponse(
                            false,
                            "Failed fetching CO daily transaction with MMS Code : " + mmsCode,
                            e.getMessage(),
                            "500"
                    ), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("stdbydb/co/daily-transaction/hist")
    public ResponseEntity getCoDailyTransactionHistoryFromStandbyDB(
            @RequestHeader("mmsCode")
            @Size(min = 3, message = "MMS Code is too short!")
            @Size(max = 10, message = "MMS Code is too long") String mmsCode,
            @RequestParam(value = "date", required = false) String date
    ) {
        try {
            List<DailyTransactionGroupedDTO> dailyTransactionGroupedDTO = dailyTransactionService.fetchAllCoDailyTransaction(mmsCode, date);
            if(!dailyTransactionGroupedDTO.isEmpty()) {
                EXTERNAL_CONTROLLER_LOGGER.error("External API for Summary DMK success with MMS Code " + mmsCode);
                return new ResponseEntity(
                        new BaseResponse(true,
                                "Success fetching CO daily transaction with MMS Code : " + mmsCode,
                                dailyTransactionGroupedDTO,
                                "200"), HttpStatus.OK);
            } else {
                return new ResponseEntity<>(HttpStatus.NO_CONTENT);
            }
        } catch (Exception e) {
            EXTERNAL_CONTROLLER_LOGGER.error("External API for Summary DMK error : " + e);
            return new ResponseEntity(new BaseResponse(
                    false,
                    "Failed fetching CO daily transaction with MMS Code : " + mmsCode,
                    e.getMessage(),
                    "500"
            ), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/personas/role/{mmsCode}/{role}")
    public ResponseEntity<List<CashUserPersonasDto>> getLogDetailsForUserChange(@PathVariable String mmsCode, @PathVariable String role) {
        List<CashUserPersonasDto> list = cashUserPersonasService.fetchAllPersonaBymmsCodeAndRole(mmsCode,role);
        if(list.size() == 0) {
            CASH_HOME_LOGGER.info("User not found for MMSCode: {} and Role : {}", mmsCode,role);
            ResponseEntity.status(HttpStatus.NOT_FOUND).body(list);
        }
        CASH_HOME_LOGGER.info("Fetched the list of User for MMSCode: {} and Role : {}", mmsCode,role);
        return ResponseEntity.status(HttpStatus.OK).body(list);
    }

    private User getUserFromKeyFactor(String keyFactor) throws UnsupportedEncodingException {
        String decoded = Utility.getDecodedBase64(keyFactor);
        String[] arrayString = decoded.split(REGEX_SPLIT);
        return new User(arrayString[0], arrayString[1]);
    }
}
