package com.btpnsyariah.agendaku.stdbydb.report.model;

import com.btpnsyariah.agendaku.cashmanager.transaction.fpb.FPBItemEntity;
import com.btpnsyariah.agendaku.cashmanager.transaction.fpb.RequisitionStatus;
import lombok.*;

import javax.persistence.*;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Builder
@ToString
@Table(name = "FPBRequest", schema = "dbo")
public class ReadOnlyFPBRequestEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "Id", nullable = false, updatable = false)
    private Long id;

    @Column(name = "RequisitionNo")
    private String requisitionNo;

    @Column(name = "MMSCode")
    private String mmsCode;

    @Column(name = "CreatedBy")
    private String createdBy;

    @Column(name = "CreatedDate")
    private Timestamp createdDate;

    @Column(name = "RequestAmount")
    private BigDecimal requestAmount;

    @Enumerated(EnumType.STRING)
    @Column(name = "RequestStatus")
    private RequisitionStatus requestStatus;

    @Column(name = "Notes")
    private String notes;

    @Column(name = "Telephone")
    private String telephone;

    @Column(name = "Address")
    private String address;

    @Column(name = "RecipientName")
    private String recipientName;

    @Column(name = "ActualizedAmount")
    private BigDecimal actualizedAmount;

    @Column(name = "LbDailyTransactionId")
    private Long lbDailyId;

    @Column(name = "AdditionalSettlementId")
    private Long additionalSettlementId;

    @Column(name = "IsReversed")
    private boolean isReversed;

    @Column(name = "RefundId")
    private Long refundId;

    @Column(name = "DisbursementAutopost")
    private boolean disbursementAutopost;

    @Column(name = "ActualizationAutopost")
    private boolean actualizationAutopost;

    @Column(name = "SettlementAutopost")
    private boolean settlementAutopost;

    @Column(name = "ReverseCounter")
    private int reverseCounter;

    @OneToMany(mappedBy = "fpbRequestEntity")
    private List<ReadOnlyFPBItemEntity> requisitionItems;

    @PrePersist
    public void prePersistNulls() {
        if (requestAmount == null) {
            requestAmount = BigDecimal.ZERO;
        }

        if (createdDate == null) {
            createdDate = new Timestamp(new Date().getTime());
        }

        if (requestStatus == null) {
            requestStatus = RequisitionStatus.SUBMITTED;
        }
    }

    public ReadOnlyFPBRequestEntity(String mmsCode, String createdBy, Timestamp createdDate, RequisitionStatus requestStatus) {
        this.mmsCode = mmsCode;
        this.createdBy = createdBy;
        this.createdDate = createdDate;
        this.requestStatus = requestStatus;
    }

}
