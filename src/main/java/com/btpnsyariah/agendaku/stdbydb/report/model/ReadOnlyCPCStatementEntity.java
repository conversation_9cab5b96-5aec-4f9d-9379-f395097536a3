package com.btpnsyariah.agendaku.stdbydb.report.model;

import com.btpnsyariah.agendaku.cashmanager.transaction.monthlytransaction.coverdana.CPCTransactionType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.math.BigDecimal;
import java.sql.Timestamp;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "CPCStatement", schema = "dbo")
public class ReadOnlyCPCStatementEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "Id", nullable = false)
    private Long id;

    @Column(name = "CDTransactionId")
    private Long cdTransactionId;

    @Column(name = "KFOTransactionId")
    private Long kfoTransactionId;

    @Column(name = "ReverseTransactionId")
    private Long rvTransactionId;

    @Column(name = "FundSource")
    private String fundSource;

    @Column(name = "City")
    private String city;

    @Column(name = "CreatedDate")
    private Timestamp createdDate;

    @Column(name = "CreatedBy")
    private String createdBy;

    @Column(name = "CreatedByName")
    private String createdByName;

    @Enumerated(EnumType.STRING)
    @Column(name = "TransactionType")
    private CPCTransactionType transactionType;

    @Column(name = "Amount")
    private BigDecimal amount;

    @Column(name = "HundredThousands", columnDefinition = "INT DEFAULT '0'")
    private Integer hundredThousands;

    @Column(name = "SeventyFiveThousands", columnDefinition = "INT DEFAULT '0'")
    private Integer seventyFiveThousands;

    @Column(name = "FiftyThousands", columnDefinition = "INT DEFAULT '0'")
    private Integer fiftyThousands;

    @Column(name = "TwentyThousands", columnDefinition = "INT DEFAULT '0'")
    private Integer twentyThousands;

    @Column(name = "TenThousands", columnDefinition = "INT DEFAULT '0'")
    private Integer tenThousands;

    @Column(name = "FiveThousands", columnDefinition = "INT DEFAULT '0'")
    private Integer fiveThousands;

    @Column(name = "TwoThousands", columnDefinition = "INT DEFAULT '0'")
    private Integer twoThousands;

    @Column(name = "OneThousands", columnDefinition = "INT DEFAULT '0'")
    private Integer oneThousands;

    @Column(name = "OneThousandCoins", columnDefinition = "INT DEFAULT '0'")
    private Integer oneThousandCoins;

    @Column(name = "FiveHundredCoins", columnDefinition = "INT DEFAULT '0'")
    private Integer fiveHundredCoins;

    @Column(name = "TwoHundredCoins", columnDefinition = "INT DEFAULT '0'")
    private Integer twoHundredCoins;

    @Column(name = "OneHundredCoins", columnDefinition = "INT DEFAULT '0'")
    private Integer oneHundredCoins;

    @Column(name = "FiftyCoins", columnDefinition = "INT DEFAULT '0'")
    private Integer fiftyCoins;

    @Column(name = "TwentyFiveCoins", columnDefinition = "INT DEFAULT '0'")
    private Integer twentyFiveCoins;

    @Column(name = "SmallMoney", columnDefinition = "INT DEFAULT '0'")
    private Integer smallMoney;

}
